# LX2160A内存诊断脚本

## U-Boot内存测试命令

### 1. 基础内存测试
```bash
# 在U-Boot提示符下执行
# 测试前8GB内存
mtest 0x80000000 0x280000000 0x55555555 1

# 测试后8GB内存  
mtest 0x2080000000 0x2280000000 0xAAAAAAAA 1

# 快速模式测试
mtest 0x80000000 0x100000000 0x12345678 1
```

### 2. 分段内存测试
```bash
# 测试第一个内存控制器管理的内存
mtest 0x80000000 0x180000000 0x55555555 10

# 测试第二个内存控制器管理的内存
mtest 0x2080000000 0x2180000000 0xAAAAAAAA 10
```

### 3. ECC测试
```bash
# 检查ECC状态
md 0x1080000 10  # DDR Controller 0 寄存器
md 0x1090000 10  # DDR Controller 1 寄存器

# 查看ECC错误计数器
md 0x1080170 1   # ECC错误状态寄存器
md 0x1090170 1
```

## 启动参数调整

### 1. 禁用ECC
```bash
# 在U-Boot中设置
setenv bootargs 'console=ttyAMA0,115200 root=/dev/mmcblk1p1 rw rootwait earlycon=pl011,mmio32,0x21c0000 mem=8G'
```

### 2. 使用单个内存控制器
```bash
# 只使用前8GB内存（单控制器）
setenv bootargs 'console=ttyAMA0,115200 root=/dev/mmcblk1p1 rw rootwait earlycon=pl011,mmio32,0x21c0000 mem=8G'
```

### 3. 内存地址范围限制
```bash
# 避开可能有问题的内存区域
setenv bootargs 'console=ttyAMA0,115200 root=/dev/mmcblk1p1 rw rootwait earlycon=pl011,mmio32,0x21c0000 mem=6G'
```

## 硬件检查清单

### 1. DDR4内存模块检查
- [ ] 检查内存模块是否正确插入
- [ ] 检查内存模块型号是否与其他正常板卡一致
- [ ] 尝试更换内存模块
- [ ] 检查内存插槽是否有物理损坏

### 2. 电源检查
- [ ] 测量DDR4电源电压（1.2V, 2.5V）
- [ ] 检查电源纹波是否在规格范围内
- [ ] 验证电源时序是否正确

### 3. 时钟检查
- [ ] 测量DDR4时钟信号质量
- [ ] 检查时钟频率是否准确（2900 MT/s）
- [ ] 验证时钟信号完整性

### 4. PCB检查
- [ ] 检查DDR4走线是否有短路或开路
- [ ] 测量阻抗匹配
- [ ] 检查过孔质量

## 故障定位步骤

### 步骤1：确定故障内存区域
```bash
# 逐步减少内存使用量
mem=4G  -> mem=2G  -> mem=1G
```

### 步骤2：隔离内存控制器
```bash
# 测试单个内存控制器
# 如果mem=8G正常，说明第二个控制器有问题
# 如果mem=4G都有问题，说明第一个控制器有问题
```

### 步骤3：ECC功能验证
```bash
# 禁用ECC看是否能正常启动
# 如果禁用ECC后正常，说明ECC功能有问题
```
