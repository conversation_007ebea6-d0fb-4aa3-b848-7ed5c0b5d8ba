# 基于原patch的最小修改差异

## 修改1: spi_imx_configure_burst_length函数 (第82-83行)

```diff
-	/* Use XCH bit control for better timing */
-	ctrl &= ~MXC_CSPICTRL_SMC;
+	/* Keep SMC=1 for DMA mode - auto-start when FIFO has data */
+	ctrl |= MXC_CSPICTRL_SMC;
```

## 修改2: spi_imx_dma_transfer函数 (第183-186行)

```diff
-	/* Start SPI transfer using XCH bit */
-	ctrl = readl(spi_imx->base + MXC_CSPICTRL);
-	ctrl |= MXC_CSPICTRL_XCH;
-	writel(ctrl, spi_imx->base + MXC_CSPICTRL);
-
-	/* Wait for DMA to finish the data transfer */
+	/* 
+	 * SMC=1 mode: transfer starts automatically when FIFO has data
+	 * No need to manually set XCH bit - this prevents DMA timeout!
+	 */
```

## 修改3: commit message更新

```diff
-3. Use XCH bit control instead of SMC for better timing control
+3. Keep SMC=1 for DMA mode (auto-start when FIFO has data)
```

## 总计修改
- **删除**: 5行代码
- **添加**: 3行代码  
- **净减少**: 2行代码
- **关键修改**: 2处位置

## 核心变化
1. **SMC控制**: 从清除SMC改为设置SMC
2. **XCH移除**: 删除手动XCH设置，避免DMA超时
3. **注释更新**: 说明SMC模式的工作原理
