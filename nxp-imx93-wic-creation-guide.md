# 🔧 NXP i.MX93 WIC镜像制作指南

本指南介绍如何在不依赖Yocto环境的情况下，手动创建适用于NXP i.MX93处理器的WIC磁盘镜像文件。

## 📋 目录

- [WIC文件格式概述](#wic文件格式概述)
- [环境准备](#环境准备)
- [分析NXP官方WIC配置](#分析nxp官方wic配置)
- [手动创建WIC镜像](#手动创建wic镜像)
- [验证与测试](#验证与测试)

## 🔍 WIC文件格式概述

### 什么是WIC文件

WIC（Wic Image Creator）是Yocto项目开发的磁盘镜像创建工具生成的镜像格式。WIC文件具有以下特点：

- **完整性**：包含完整的分区表、引导程序、内核和根文件系统
- **可烧录性**：可直接烧录到SD卡、eMMC等存储设备
- **分区化**：支持多分区布局，每个分区可有不同的文件系统
- **兼容性**：与标准磁盘镜像格式兼容

### i.MX93典型分区布局

| 分区 | 起始位置 | 大小 | 文件系统 | 内容 | 说明 |
|------|----------|------|----------|------|------|
| u-boot | 1KB | 4MB | raw | imx-boot | U-Boot引导程序 |
| boot | 4MB | 64MB | FAT32 | Image, *.dtb | 内核和设备树 |
| rootfs | 68MB | 剩余 | ext4 | 完整根文件系统 | Linux根文件系统 |

## 🛠️ 环境准备

### 安装必要工具

```bash
# 安装基础工具
sudo apt update
sudo apt install -y parted dosfstools e2fsprogs mtools util-linux zstd
```

### 创建工作环境

```bash
# 创建工作目录
mkdir -p ~/imx93_wic_work
cd ~/imx93_wic_work
```

## 📊 分析NXP官方WIC配置

### NXP Yocto 6.6.52版本WIC配置

基于NXP官方Yocto BSP L6.6.52_2.2.0版本的WIC配置：

**官方WIC配置参考：**
```
# NXP i.MX93-EVK官方WIC配置 (基于Yocto 6.6.52)
# 参考: meta-imx/wic/imx-uboot-bootpart.wks.in

# Boot分区 - 包含U-Boot SPL和U-Boot proper
part u-boot --source rawcopy --sourceparams="file=imx-boot" --ondisk mmcblk --no-table --align 1 --size 4

# Kernel分区 - 包含内核镜像和设备树
part /boot --source bootimg-partition --ondisk mmcblk --fstype=vfat --label boot --active --align 4 --size 64 --use-uuid

# 根文件系统分区
part / --source rootfs --ondisk mmcblk --fstype=ext4 --label root --align 4 --use-uuid
```

## 🔧 手动创建WIC镜像

### 基本参数配置

```bash
# 设置基本参数
WIC_NAME="imx93evk-custom"
UBOOT_SIZE_MB=4
BOOT_SIZE_MB=64
ROOTFS_SIZE_MB=2048
TOTAL_SIZE_MB=$((UBOOT_SIZE_MB + BOOT_SIZE_MB + ROOTFS_SIZE_MB + 10))
```

### 创建Boot分区镜像

```bash
# 创建Boot分区镜像 (FAT32)
dd if=/dev/zero of=boot.vfat bs=1M count=$BOOT_SIZE_MB
mkfs.vfat -F 32 -n "BOOT" boot.vfat

# 添加内核和设备树到Boot分区
mcopy -i boot.vfat /path/to/Image ::
mcopy -i boot.vfat /path/to/*.dtb ::
```

### 创建根文件系统镜像

```bash
# 创建根文件系统镜像 (ext4)
dd if=/dev/zero of=rootfs.ext4 bs=1M count=$ROOTFS_SIZE_MB
mkfs.ext4 -F -L "rootfs" rootfs.ext4

# 挂载并解压根文件系统
mkdir -p /tmp/rootfs_mount
sudo mount -o loop rootfs.ext4 /tmp/rootfs_mount
sudo tar -xjf /path/to/rootfs.tar.bz2 -C /tmp/rootfs_mount
sudo umount /tmp/rootfs_mount
```

### 组装完整WIC镜像

```bash
# 创建空的WIC镜像文件
dd if=/dev/zero of=${WIC_NAME}.wic bs=1M count=$TOTAL_SIZE_MB

# 创建分区表
parted ${WIC_NAME}.wic mklabel msdos

# 创建分区
parted ${WIC_NAME}.wic mkpart primary ${UBOOT_SIZE_MB}MiB $((UBOOT_SIZE_MB + BOOT_SIZE_MB))MiB
parted ${WIC_NAME}.wic mkpart primary ext4 $((UBOOT_SIZE_MB + BOOT_SIZE_MB))MiB $((UBOOT_SIZE_MB + BOOT_SIZE_MB + ROOTFS_SIZE_MB))MiB
parted ${WIC_NAME}.wic set 1 boot on

# 写入U-Boot
dd if=/path/to/imx-boot of=${WIC_NAME}.wic bs=1K seek=1 conv=notrunc

# 写入Boot分区
BOOT_OFFSET=$((UBOOT_SIZE_MB * 1024 * 1024))
dd if=boot.vfat of=${WIC_NAME}.wic bs=1 seek=$BOOT_OFFSET conv=notrunc

# 写入根文件系统分区
ROOTFS_OFFSET=$(((UBOOT_SIZE_MB + BOOT_SIZE_MB) * 1024 * 1024))
dd if=rootfs.ext4 of=${WIC_NAME}.wic bs=1 seek=$ROOTFS_OFFSET conv=notrunc

# 清理临时文件
rm -f boot.vfat rootfs.ext4
```

### 自定义分区布局示例

**双系统启动配置：**
```
# U-Boot分区
part u-boot --source rawcopy --sourceparams="file=imx-boot" --ondisk mmcblk --no-table --align 1 --size 4

# Boot分区A (主系统)
part /boot-a --source bootimg-partition --ondisk mmcblk --fstype=vfat --label boot-a --align 4 --size 64

# Boot分区B (备份系统)
part /boot-b --source bootimg-partition --ondisk mmcblk --fstype=vfat --label boot-b --align 4 --size 64

# 根文件系统A
part /rootfs-a --source rootfs --ondisk mmcblk --fstype=ext4 --label rootfs-a --align 4 --size 1024

# 根文件系统B
part /rootfs-b --source rootfs --ondisk mmcblk --fstype=ext4 --label rootfs-b --align 4 --size 1024

# 用户数据分区
part /data --ondisk mmcblk --fstype=ext4 --label userdata --align 4 --size 2048
```

## ✅ 验证与测试

### WIC镜像分析

```bash
# 查看WIC文件信息
file ${WIC_NAME}.wic
ls -lh ${WIC_NAME}.wic

# 查看分区表信息
fdisk -l ${WIC_NAME}.wic
parted ${WIC_NAME}.wic print
```

### 分区挂载测试

```bash
# 测试Boot分区挂载
BOOT_OFFSET=$((4 * 1024 * 1024))  # 4MB
mkdir -p /tmp/test_boot
sudo mount -o loop,offset=$BOOT_OFFSET ${WIC_NAME}.wic /tmp/test_boot
ls -la /tmp/test_boot
sudo umount /tmp/test_boot

# 测试根文件系统分区
ROOTFS_OFFSET=$(((4 + 64) * 1024 * 1024))  # 4MB + 64MB
mkdir -p /tmp/test_rootfs
sudo mount -o loop,offset=$ROOTFS_OFFSET ${WIC_NAME}.wic /tmp/test_rootfs
ls -la /tmp/test_rootfs | head -10
sudo umount /tmp/test_rootfs
```

### 分区提取

```bash
# 提取Boot分区
dd if=${WIC_NAME}.wic of=boot_partition.img bs=512 skip=8192 count=131072

# 提取根文件系统分区
dd if=${WIC_NAME}.wic of=rootfs_partition.img bs=512 skip=139264 count=4194304
```

### 部署测试

```bash
# 烧录到SD卡
sudo dd if=${WIC_NAME}.wic of=/dev/sdX bs=1M status=progress
sync

# 验证烧录结果
sudo fdisk -l /dev/sdX
```

## 📚 总结

本指南提供了简洁的i.MX93 WIC镜像制作方案：

- ✅ **简单易用**：只需几个核心命令
- ✅ **完全独立**：不依赖Yocto环境
- ✅ **灵活配置**：支持自定义分区布局
- ✅ **完整验证**：包含测试和验证步骤
- ✅ **实用性强**：基于NXP官方配置

通过本指南，您可以轻松创建和管理i.MX93的WIC镜像文件。

> **相关文档**：如需了解根文件系统的修改，请参考《NXP i.MX93 根文件系统修改指南》。
