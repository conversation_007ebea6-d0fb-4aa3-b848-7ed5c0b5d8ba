# i.MX6ULL ECSPI DMA vs PIO 寄存器分析与修复方案

## 问题概述

通过对比DMA模式和PIO模式下的ECSPI寄存器dump，发现了导致DMA模式下字节间间隔的根本原因。

## 寄存器差异分析

### 关键差异对比表

| 寄存器 | 字段 | DMA模式 | PIO模式 | 差异说明 |
|--------|------|---------|---------|----------|
| CONREG | BURST_LENGTH(20..31) | 0x7 (7+1=8 bits) | 0xfff (4095+1=4096 bits) | **关键差异** |
| CONREG | SMC(3..3) | 0x1 | 0x0 | Start Mode Control |
| INTREG | RREN(3..3) | 0x0 | 0x1 | RX Ready中断使能 |
| DMAREG | TEDEN(7..7) | 0x1 | 0x0 | TX DMA使能 |
| DMAREG | RXDEN(23..23) | 0x1 | 0x0 | RX DMA使能 |
| DMAREG | RX_THRESHOLD(16..21) | 0x1f (31) | 0x0 | RX阈值 |

### 核心问题分析

#### 1. BURST_LENGTH配置错误（单位：bit）
- **DMA模式**: BURST_LENGTH = 7 → 8 bits = 1 byte
- **PIO模式**: BURST_LENGTH = 4095 → 4096 bits = 512 bytes

**问题根因**: DMA模式下burst length设置为仅8位（1字节），导致每传输1字节就会产生一次CS切换和传输间隔。这是造成字节间间隔的直接原因。

**重要说明**: BURST_LENGTH字段的值是实际burst长度减1，单位是bit而不是byte。因此：
- 值7 = 8 bits = 1 byte
- 值4095 = 4096 bits = 512 bytes

#### 2. Start Mode Control (SMC)
- **DMA模式**: SMC = 1 (立即开始传输)
- **PIO模式**: SMC = 0 (XCH位控制传输)

#### 3. DMA阈值配置分析
- **DMA模式**: RX_THRESHOLD = 31 (FIFO中有31个字时才触发DMA)
- **PIO模式**: RX_THRESHOLD = 0 (不使用DMA)

**阈值问题**: RX_THRESHOLD设置为31意味着FIFO中必须积累31个32位字（124字节）才会触发DMA传输。但由于BURST_LENGTH只有1字节，FIFO永远不会积累到31个字，导致DMA传输效率低下。

## 根本原因

1. **Burst Length严重过小**: DMA模式下BURST_LENGTH设置为8位（1字节），导致每传输1字节就产生CS切换和传输间隔
2. **DMA阈值与Burst Length不匹配**: RX_THRESHOLD=31但BURST_LENGTH=1字节，阈值永远无法达到
3. **传输模式控制问题**: SMC=1导致立即开始传输，与DMA时序不匹配

## 解决方案

### 方案1: 修改内核驱动 (推荐)

#### 1.1 修改burst length计算逻辑（关键修复）

```c
// 文件: drivers/spi/spi-imx.c
// 函数: spi_imx_prepare_for_dma()

static int spi_imx_prepare_for_dma(struct spi_imx_data *spi_imx,
                                   struct spi_transfer *transfer)
{
    u32 ctrl;
    unsigned int burst_length_bits;

    // 计算burst length（单位：bit）
    // 原始代码错误地设置了很小的burst length

    // 对于1024字节传输，设置为1024字节 = 8192 bits
    if (transfer->len >= 1024) {
        burst_length_bits = 8192;  // 1024 bytes * 8 bits/byte
    } else if (transfer->len >= 512) {
        burst_length_bits = 4096;  // 512 bytes * 8 bits/byte
    } else if (transfer->len >= 64) {
        burst_length_bits = transfer->len * 8;  // 转换为bits
    } else {
        burst_length_bits = 64 * 8;  // 最小64字节 = 512 bits
    }

    // 硬件限制：最大4096 bits (BURST_LENGTH最大值4095+1)
    burst_length_bits = min(burst_length_bits, 4096U);

    ctrl = readl(spi_imx->base + MXC_CSPICTRL);
    ctrl &= ~MXC_CSPICTRL_BL_MASK;
    // BURST_LENGTH字段值 = 实际长度 - 1
    ctrl |= (burst_length_bits - 1) << MXC_CSPICTRL_BL_OFFSET;
    writel(ctrl, spi_imx->base + MXC_CSPICTRL);

    return 0;
}
```

#### 1.2 优化DMA阈值配置

```c
// 修改DMA阈值设置
static void spi_imx_dma_configure(struct spi_imx_data *spi_imx)
{
    u32 dma_reg;

    dma_reg = readl(spi_imx->base + MXC_CSPIDMAREG);

    // 设置合理的RX阈值
    // RX_THRESHOLD=16更合适：既不会太频繁触发DMA，也不会等待太久
    dma_reg &= ~(0x3F << 16);  // 清除RX_THRESHOLD
    dma_reg |= (16 << 16);     // 设置RX_THRESHOLD为16（16个32位字=64字节）

    // 设置TX阈值
    // TX_THRESHOLD=16：当FIFO中少于16个字时触发DMA填充
    dma_reg &= ~0x3F;          // 清除TX_THRESHOLD
    dma_reg |= 16;             // 设置TX_THRESHOLD为16

    // 启用DMA
    dma_reg |= (1 << 7);       // TEDEN: TX FIFO Empty DMA Enable
    dma_reg |= (1 << 23);      // RXDEN: RX FIFO DMA Enable

    writel(dma_reg, spi_imx->base + MXC_CSPIDMAREG);
}

/*
 * DMA阈值说明：
 * - RX_THRESHOLD=16: FIFO中有16个32位字（64字节）时触发RX DMA
 * - TX_THRESHOLD=16: FIFO中少于16个32位字时触发TX DMA填充
 * - 16是一个平衡值：不会太频繁触发DMA，也能保证数据流的连续性
 * - FIFO总深度是64个32位字，16是1/4深度，比较合理
 */
```

#### 1.3 修改传输控制逻辑

```c
// 修改传输启动逻辑
static void spi_imx_dma_transfer(struct spi_imx_data *spi_imx,
                                struct spi_transfer *transfer)
{
    u32 ctrl;
    
    // 配置控制寄存器
    ctrl = readl(spi_imx->base + MXC_CSPICTRL);
    
    // 设置SMC为0，使用XCH位控制传输
    ctrl &= ~MXC_CSPICTRL_SMC;
    
    // 启用SPI
    ctrl |= MXC_CSPICTRL_EN;
    
    writel(ctrl, spi_imx->base + MXC_CSPICTRL);
    
    // 启动DMA传输
    dmaengine_submit(spi_imx->dma_tx_desc);
    dmaengine_submit(spi_imx->dma_rx_desc);
    dma_async_issue_pending(spi_imx->dma_tx);
    dma_async_issue_pending(spi_imx->dma_rx);
    
    // 启动SPI传输
    ctrl |= MXC_CSPICTRL_XCH;
    writel(ctrl, spi_imx->base + MXC_CSPICTRL);
}
```

### 方案2: 内核补丁文件

创建针对Linux 4.19.35的补丁：

```patch
--- a/drivers/spi/spi-imx.c
+++ b/drivers/spi/spi-imx.c
@@ -XXX,XX +XXX,XX @@ static int spi_imx_dma_configure(struct spi_imx_data *spi_imx)
 {
     struct dma_slave_config config;
     int ret;
+    u32 dma_reg;
 
     /* Prepare the TX dma transfer */
     config.direction = DMA_MEM_TO_DEV;
@@ -XXX,XX +XXX,XX @@ static int spi_imx_dma_configure(struct spi_imx_data *spi_imx)
     config.dst_addr = spi_imx->base + MXC_CSPITXDATA;
     config.dst_addr_width = spi_imx->bytes_per_word;
-    config.dst_maxburst = spi_imx->wml;
+    config.dst_maxburst = min(spi_imx->wml, 8U);
     ret = dmaengine_slave_config(spi_imx->dma_tx, &config);
     if (ret) {
         dev_err(spi_imx->dev, "TX dma configuration failed with %d\n", ret);
@@ -XXX,XX +XXX,XX @@ static int spi_imx_dma_configure(struct spi_imx_data *spi_imx)
     config.direction = DMA_DEV_TO_MEM;
     config.src_addr = spi_imx->base + MXC_CSPIRXDATA;
     config.src_addr_width = spi_imx->bytes_per_word;
-    config.src_maxburst = spi_imx->wml;
+    config.src_maxburst = min(spi_imx->wml, 8U);
     ret = dmaengine_slave_config(spi_imx->dma_rx, &config);
     if (ret) {
         dev_err(spi_imx->dev, "RX dma configuration failed with %d\n", ret);
         return ret;
     }
 
+    /* Configure DMA thresholds */
+    dma_reg = readl(spi_imx->base + MXC_CSPIDMAREG);
+    dma_reg &= ~(0x3F << 16);  /* Clear RX_THRESHOLD */
+    dma_reg |= (8 << 16);      /* Set RX_THRESHOLD to 8 */
+    dma_reg &= ~0x3F;          /* Clear TX_THRESHOLD */
+    dma_reg |= 8;              /* Set TX_THRESHOLD to 8 */
+    writel(dma_reg, spi_imx->base + MXC_CSPIDMAREG);
+
     return 0;
 }
 
@@ -XXX,XX +XXX,XX @@ static int spi_imx_dma_transfer(struct spi_imx_data *spi_imx,
                                 struct spi_transfer *transfer)
 {
     struct dma_async_tx_descriptor *desc_tx, *desc_rx;
-    unsigned long transfer_timeout;
-    unsigned long timeout;
+    unsigned long transfer_timeout, timeout;
     struct spi_master *master = spi_imx->bitbang.master;
     struct sg_table *tx = &transfer->tx_sg, *rx = &transfer->rx_sg;
+    u32 ctrl;
+    unsigned int burst_length;
+
+    /* Calculate optimal burst length */
+    if (transfer->len <= 512) {
+        burst_length = transfer->len;
+    } else {
+        burst_length = 512;
+    }
+    burst_length = min(burst_length, 4095U);
+
+    /* Configure burst length */
+    ctrl = readl(spi_imx->base + MXC_CSPICTRL);
+    ctrl &= ~MXC_CSPICTRL_BL_MASK;
+    ctrl |= (burst_length - 1) << MXC_CSPICTRL_BL_OFFSET;
+    ctrl &= ~MXC_CSPICTRL_SMC;  /* Use XCH bit control */
+    writel(ctrl, spi_imx->base + MXC_CSPICTRL);
 
     /*
      * The TX DMA setup starts the transfer, so make sure RX is configured
```

### 方案3: 设备树配置优化

```dts
&ecspi2 {
    pinctrl-names = "default";
    pinctrl-0 = <&pinctrl_ecspi2>;
    status = "okay";
    
    /* 优化DMA配置 */
    dmas = <&sdma 7 7 1>, <&sdma 8 7 2>;
    dma-names = "rx", "tx";
    
    /* 设置合适的FIFO水位 */
    fsl,spi-rdy-drctl = <0>;
    
    /* 优化时钟频率 */
    spi-max-frequency = <20000000>;
    
    spidev@0 {
        compatible = "rohm,dh2228fv";
        spi-max-frequency = <20000000>;
        reg = <0>;
    };
};
```

## 验证方法

### 1. 寄存器验证脚本

```bash
#!/bin/bash
# verify_ecspi_config.sh

echo "=== ECSPI2寄存器配置验证 ==="

# 读取关键寄存器
CONREG=$(./memtool ECSPI2.CONREG | grep "Value:" | awk '{print $3}')
DMAREG=$(./memtool ECSPI2.DMAREG | grep "Value:" | awk '{print $3}')

echo "CONREG: $CONREG"
echo "DMAREG: $DMAREG"

# 解析BURST_LENGTH
BURST_LENGTH=$(((0x$CONREG >> 20) & 0xFFF))
echo "Burst Length: $BURST_LENGTH"

# 解析DMA阈值
RX_THRESHOLD=$(((0x$DMAREG >> 16) & 0x3F))
TX_THRESHOLD=$((0x$DMAREG & 0x3F))
echo "RX Threshold: $RX_THRESHOLD"
echo "TX Threshold: $TX_THRESHOLD"

# 检查配置是否正确
if [ $BURST_LENGTH -ge 512 ]; then
    echo "✓ Burst Length配置正确"
else
    echo "✗ Burst Length配置过小: $BURST_LENGTH"
fi

if [ $RX_THRESHOLD -le 16 ]; then
    echo "✓ RX Threshold配置合理"
else
    echo "✗ RX Threshold配置过高: $RX_THRESHOLD"
fi
```

### 2. 性能测试

```c
// spi_timing_test.c
#include <linux/spi/spidev.h>
#include <sys/time.h>

void test_spi_continuity(void)
{
    int fd = open("/dev/spidev1.0", O_RDWR);
    uint8_t tx_buf[1024];
    uint8_t rx_buf[1024];
    struct timeval start, end;
    
    // 填充测试数据
    for (int i = 0; i < 1024; i++) {
        tx_buf[i] = i & 0xFF;
    }
    
    struct spi_ioc_transfer tr = {
        .tx_buf = (unsigned long)tx_buf,
        .rx_buf = (unsigned long)rx_buf,
        .len = 1024,
        .speed_hz = 20000000,
        .bits_per_word = 8,
    };
    
    gettimeofday(&start, NULL);
    int ret = ioctl(fd, SPI_IOC_MESSAGE(1), &tr);
    gettimeofday(&end, NULL);
    
    long duration = (end.tv_sec - start.tv_sec) * 1000000 + 
                   (end.tv_usec - start.tv_usec);
    
    printf("Transfer result: %d\n", ret);
    printf("Duration: %ld us\n", duration);
    printf("Effective rate: %.2f MB/s\n", 
           (1024.0 * 1000000) / duration / 1024 / 1024);
    
    close(fd);
}
```

## 实施建议

1. **立即解决**: 应用内核补丁，重新编译内核
2. **验证效果**: 使用示波器观察SPI波形，确认字节间间隔消除
3. **性能测试**: 运行测试程序验证传输连续性和效率
4. **长期优化**: 考虑升级到更新的内核版本

## 预期效果

- 消除DMA模式下的字节间间隔
- 提高大数据块传输效率
- 保持与PIO模式相同的传输连续性
- 支持1024字节及更大数据块的连续传输

## 文件清单

本次分析生成了以下文件：

1. **ecspi-register-analysis-and-fix.md** - 详细的寄存器分析和解决方案
2. **0001-spi-imx-fix-dma-burst-length-for-continuous-transfer.patch** - 内核补丁文件
3. **apply-ecspi-fix.sh** - 自动应用补丁的脚本
4. **test_ecspi_fix.c** - 测试程序（由脚本生成）

## 快速实施指南

### 步骤1: 应用补丁
```bash
# 在Linux开发环境中执行
./apply-ecspi-fix.sh
```

### 步骤2: 重新编译内核
```bash
cd /path/to/linux-4.19.35
make ARCH=arm CROSS_COMPILE=arm-linux-gnueabihf- zImage modules dtbs
```

### 步骤3: 部署和测试
```bash
# 部署新内核到目标设备
# 重启设备
# 运行测试程序验证修复效果
./test_ecspi_fix
```

### 步骤4: 验证寄存器配置
```bash
# 在目标设备上检查寄存器
./memtool ECSPI2.CONREG  # 应该看到BURST_LENGTH >= 512
./memtool ECSPI2.DMAREG  # 应该看到合理的阈值设置
```

## 技术要点总结

- **根本问题**: DMA模式下BURST_LENGTH设置为7（8 bits = 1字节），导致每字节后都有间隔
- **单位理解**: BURST_LENGTH字段单位是bits不是bytes，这是关键认知错误
- **关键修复**: 将BURST_LENGTH调整为4095（4096 bits = 512字节），优化DMA阈值为16字（64字节）
- **传输控制**: 使用XCH位控制而非SMC，提供更好的时序控制
- **性能提升**: 从1024次CS切换减少到2次，传输效率提升约500倍
- **兼容性**: 保持对小数据传输的兼容性，仅优化大数据块传输

## 关键认知纠正

**重要**: 之前的分析中错误地认为BURST_LENGTH单位是字节，实际上：
- BURST_LENGTH字段单位是**bits**（位）
- DMA模式: BURST_LENGTH=7 → 8 bits = 1 byte
- PIO模式: BURST_LENGTH=4095 → 4096 bits = 512 bytes
- 这解释了为什么DMA模式每传输1字节就有间隔
