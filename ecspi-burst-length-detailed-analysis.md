# i.MX6ULL ECSPI BURST_LENGTH 详细技术分析

## 关键发现：BURST_LENGTH单位是bit，不是byte

### 寄存器值重新解读

#### DMA模式寄存器分析
```
ECSPI2.CONREG Value: 0x007010F9
BURST_LENGTH(20..31) = 0x7 = 7
实际burst长度 = 7 + 1 = 8 bits = 1 byte
```

#### PIO模式寄存器分析  
```
ECSPI2.CONREG Value: 0xFFF010F1
BURST_LENGTH(20..31) = 0xfff = 4095
实际burst长度 = 4095 + 1 = 4096 bits = 512 bytes
```

### 问题根因分析

#### 1. 字节间间隔的直接原因
- **DMA模式**: 每次只传输1字节（8 bits），然后CS信号切换
- **PIO模式**: 一次传输512字节（4096 bits），CS信号保持有效
- **结果**: DMA模式在每个字节后都有间隔，PIO模式连续传输

#### 2. DMA阈值与burst length不匹配
```
DMA模式配置问题：
- BURST_LENGTH = 8 bits (1 byte)
- RX_THRESHOLD = 31 words (124 bytes)
- 矛盾：每次只传输1字节，但要等FIFO积累124字节才触发DMA
```

#### 3. 传输时序分析
```
DMA模式传输序列（错误）：
1. 传输1字节 → CS切换 → 间隔
2. 传输1字节 → CS切换 → 间隔  
3. 重复1024次...

PIO模式传输序列（正确）：
1. 传输512字节 → 连续无间隔
2. 传输512字节 → 连续无间隔
```

## 技术规格说明

### BURST_LENGTH字段详解
- **位置**: CONREG[31:20]
- **单位**: bits（位）
- **范围**: 0-4095 (实际长度1-4096 bits)
- **计算**: 实际长度 = BURST_LENGTH + 1
- **最大**: 4096 bits = 512 bytes

### DMA阈值字段详解
- **RX_THRESHOLD**: DMAREG[21:16]，单位是32位字
- **TX_THRESHOLD**: DMAREG[5:0]，单位是32位字
- **换算**: 1 word = 4 bytes
- **FIFO深度**: 64 words = 256 bytes

## 修复策略

### 策略1: 最大化burst length
```c
// 对于1024字节传输
burst_length_bits = min(1024 * 8, 4096);  // 4096 bits = 512 bytes
BURST_LENGTH = burst_length_bits - 1 = 4095;
```

### 策略2: 平衡的DMA阈值
```c
// 设置合理的阈值
RX_THRESHOLD = 16;  // 16 words = 64 bytes
TX_THRESHOLD = 16;  // 16 words = 64 bytes

// 理由：
// 1. 不会太频繁触发DMA（避免开销）
// 2. 不会等待太久（保证响应性）
// 3. 与512字节burst length匹配良好
```

### 策略3: 分段传输
```c
// 对于大于512字节的传输，分段处理
if (transfer_len > 512) {
    // 第一段：512字节 (4096 bits)
    burst_length_bits = 4096;
    // 后续段：根据剩余长度调整
}
```

## 性能影响分析

### 修复前（DMA模式）
```
传输1024字节：
- 需要1024次burst
- 每次传输1字节
- 1024次CS切换
- 大量传输间隔
```

### 修复后（DMA模式）
```
传输1024字节：
- 需要2次burst (512+512字节)
- 每次传输512字节  
- 2次CS切换
- 最小传输间隔
```

### 性能提升预期
- **传输连续性**: 从1024个间隔减少到1个间隔
- **CS切换次数**: 从1024次减少到2次
- **传输效率**: 提升约500倍
- **波形质量**: 接近PIO模式的连续性

## 硬件限制和约束

### ECSPI硬件限制
1. **最大burst length**: 4096 bits (512 bytes)
2. **FIFO深度**: 64 words (256 bytes)
3. **DMA阈值范围**: 0-63 words

### 设计约束
1. **大传输分段**: >512字节需要分段
2. **阈值平衡**: 不能太高（延迟）或太低（频繁中断）
3. **兼容性**: 保持小传输的兼容性

## 验证方法

### 1. 寄存器验证
```bash
# 修复后应该看到：
./memtool ECSPI2.CONREG
# BURST_LENGTH应该是4095 (0xFFF)

./memtool ECSPI2.DMAREG  
# RX_THRESHOLD应该是16
# TX_THRESHOLD应该是16
```

### 2. 波形验证
- 使用示波器观察SPI CLK和MOSI信号
- 修复前：每字节后有明显间隔
- 修复后：512字节内连续，只在512字节边界有短暂间隔

### 3. 性能测试
```c
// 测试1024字节传输时间
// 修复前：~10ms（包含间隔时间）
// 修复后：~0.4ms（20MHz时的理论时间）
```

## 实施建议

### 立即修复
1. 应用提供的内核补丁
2. 重新编译内核
3. 验证寄存器配置

### 长期优化
1. 考虑动态调整burst length
2. 根据传输大小优化DMA阈值
3. 实现更智能的分段策略

### 测试覆盖
1. 不同大小的传输（64B, 512B, 1024B, 2048B）
2. 不同SPI频率下的测试
3. 连续传输的稳定性测试

## 总结

通过正确理解BURST_LENGTH字段的单位（bits而非bytes），我们发现DMA模式下设置的8位burst length是导致字节间间隔的根本原因。修复方案将burst length增加到4096位（512字节），并优化DMA阈值为16字（64字节），可以完全消除字节间间隔问题，实现与PIO模式相同的传输连续性。
