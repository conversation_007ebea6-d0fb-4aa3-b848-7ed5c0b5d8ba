diff --git a/drivers/spi/spi-imx.c b/drivers/spi/spi-imx.c
index 0b9531afed0e..3cebf6b123fc 100644
--- a/drivers/spi/spi-imx.c
+++ b/drivers/spi/spi-imx.c
@@ -34,6 +34,10 @@
 #define MXC_CSPIINT		0x0c
 #define MXC_RESET		0x1c
 
+#define MXC_CSPICTRL_BL_OFFSET MX51_ECSPI_CTRL_BL_OFFSET
+#define MXC_CSPICTRL_BL_MASK MX51_ECSPI_CTRL_BL_MASK
+#define MXC_CSPIDMAREG 0x14
+
 /* generic defines to abstract from the different register layouts */
 #define MXC_INT_RR	(1 << 0) /* Receive data ready interrupt */
 #define MXC_INT_TE	(1 << 1) /* Transmit FIFO empty interrupt */
@@ -109,6 +113,8 @@ struct spi_imx_data {
 	struct completion dma_rx_completion;
 	struct completion dma_tx_completion;
 
+	bool use_dma_burst_config;
+
 	const struct spi_imx_devtype_data *devtype_data;
 };
 
@@ -279,6 +285,80 @@ static bool spi_imx_can_dma(struct spi_master *master, struct spi_device *spi,
 #define MX51_ECSPI_TESTREG	0x20
 #define MX51_ECSPI_TESTREG_LBC	BIT(31)
 
+
+/*
+ * Configure optimal burst length for DMA transfers
+ * BURST_LENGTH unit is bits, not bytes!
+ * Original issue: DMA mode sets only 8 bits (1 byte), causing gaps after each byte
+ */
+static void spi_imx_configure_burst_length(struct spi_imx_data *spi_imx,
+					   struct spi_transfer *transfer)
+{
+	u32 ctrl;
+	unsigned int burst_length_bits;
+
+	/* Calculate burst length in bits based on transfer size */
+	if (transfer->len >= 1024) {
+		/* Large transfers: use 1024 bytes = 8192 bits */
+		burst_length_bits = 8192;
+	} else if (transfer->len >= 512) {
+		/* Medium transfers: use 512 bytes = 4096 bits */
+		burst_length_bits = 4096;
+	} else if (transfer->len >= 64) {
+		/* Small transfers: use actual length in bits */
+		burst_length_bits = transfer->len * 8;
+	} else {
+		/* Very small transfers: minimum 64 bytes = 512 bits */
+		burst_length_bits = 512;
+	}
+
+	/* Hardware limit is 4096 bits (BURST_LENGTH max value 4095+1) */
+	burst_length_bits = min(burst_length_bits, 4096U);
+
+	/* Configure burst length in CONREG */
+	ctrl = readl(spi_imx->base + MXC_CSPICTRL);
+	ctrl &= ~MXC_CSPICTRL_BL_MASK;
+	/* BURST_LENGTH field value = actual_length - 1 */
+	ctrl |= (burst_length_bits - 1) << MXC_CSPICTRL_BL_OFFSET;
+
+	/* Keep SMC=1 for DMA mode - auto-start when FIFO has data */
+	ctrl |= MX51_ECSPI_CTRL_SMC;
+
+	writel(ctrl, spi_imx->base + MXC_CSPICTRL);
+}
+
+/*
+ * Configure DMA thresholds for optimal performance
+ * Threshold unit is 32-bit words, not bytes
+ * Original issue: RX_THRESHOLD=31 but BURST_LENGTH=1 byte, threshold never reached
+ */
+static void spi_imx_configure_dma_thresholds(struct spi_imx_data *spi_imx)
+{
+	u32 dma_reg;
+
+	dma_reg = readl(spi_imx->base + MXC_CSPIDMAREG);
+
+	/* Clear existing threshold settings */
+	dma_reg &= ~(0x3F << 16);  /* Clear RX_THRESHOLD */
+	dma_reg &= ~0x3F;          /* Clear TX_THRESHOLD */
+
+	/* Set balanced thresholds for continuous transfer */
+	/* RX_THRESHOLD=16: trigger DMA when 16 words (64 bytes) in RX FIFO */
+	/* TX_THRESHOLD=16: trigger DMA when less than 16 words in TX FIFO */
+	dma_reg |= (16 << 16);     /* RX_THRESHOLD = 16 words = 64 bytes */
+	dma_reg |= 16;             /* TX_THRESHOLD = 16 words = 64 bytes */
+
+	/* Enable DMA requests */
+	dma_reg |= (1 << 7);       /* TEDEN: TX FIFO Empty DMA Enable */
+	dma_reg |= (1 << 23);      /* RXDEN: RX FIFO DMA Enable */
+
+	/* Disable RX DMA length and tail DMA for now */
+	dma_reg &= ~(0x3F << 24);  /* Clear RX_DMA_LENGTH */
+	dma_reg &= ~(1 << 31);     /* Clear RXTDEN */
+
+	writel(dma_reg, spi_imx->base + MXC_CSPIDMAREG);
+}
+
 static void spi_imx_buf_rx_swap_u32(struct spi_imx_data *spi_imx)
 {
 	unsigned int val = readl(spi_imx->base + MXC_CSPIRXDATA);
@@ -1149,10 +1229,14 @@ static int spi_imx_dma_configure(struct spi_master *master)
 		return -EINVAL;
 	}
 
+	printk("bytes_per_word %d, wml %d \n", buswidth, spi_imx->wml);
+
 	tx.direction = DMA_MEM_TO_DEV;
 	tx.dst_addr = spi_imx->base_phys + MXC_CSPITXDATA;
 	tx.dst_addr_width = buswidth;
-	tx.dst_maxburst = spi_imx->wml;
+	/* Reduce maxburst to prevent FIFO overflow */
+	tx.dst_maxburst = min(spi_imx->wml, 8U);
+	//tx.dst_maxburst = spi_imx->wml;
 	ret = dmaengine_slave_config(master->dma_tx, &tx);
 	if (ret) {
 		dev_err(spi_imx->dev, "TX dma configuration failed with %d\n", ret);
@@ -1162,13 +1246,18 @@ static int spi_imx_dma_configure(struct spi_master *master)
 	rx.direction = DMA_DEV_TO_MEM;
 	rx.src_addr = spi_imx->base_phys + MXC_CSPIRXDATA;
 	rx.src_addr_width = buswidth;
-	rx.src_maxburst = spi_imx->wml;
+	/* Reduce maxburst to prevent FIFO overflow */
+	rx.src_maxburst = min(spi_imx->wml, 8U);
+	//rx.src_maxburst = spi_imx->wml;
 	ret = dmaengine_slave_config(master->dma_rx, &rx);
 	if (ret) {
 		dev_err(spi_imx->dev, "RX dma configuration failed with %d\n", ret);
 		return ret;
 	}
 
+	/* Configure DMA thresholds */
+	spi_imx_configure_dma_thresholds(spi_imx);
+
 	return 0;
 }
 
@@ -1335,12 +1424,17 @@ static int spi_imx_dma_transfer(struct spi_imx_data *spi_imx,
 	if (ret)
 		return ret;
 
+#if 0
 	if (!spi_imx->devtype_data->setup_wml) {
 		dev_err(spi_imx->dev, "No setup_wml()?\n");
 		return -EINVAL;
 	}
 
 	spi_imx->devtype_data->setup_wml(spi_imx);
+#else
+	/* Configure optimal burst length for this transfer */
+	spi_imx_configure_burst_length(spi_imx, transfer);
+#endif
 
 	/*
 	 * The TX DMA setup starts the transfer, so make sure RX is configured
@@ -1648,6 +1742,8 @@ static int spi_imx_probe(struct platform_device *pdev)
 	spi_imx->bitbang.master->slave_abort = spi_imx_slave_abort;
 	spi_imx->bitbang.master->mode_bits = SPI_CPOL | SPI_CPHA | SPI_CS_HIGH \
 					     | SPI_NO_CS;
+	spi_imx->use_dma_burst_config = true;
+
 	if (is_imx35_cspi(spi_imx) || is_imx51_ecspi(spi_imx) ||
 	    is_imx53_ecspi(spi_imx))
 		spi_imx->bitbang.master->mode_bits |= SPI_LOOP | SPI_READY;
