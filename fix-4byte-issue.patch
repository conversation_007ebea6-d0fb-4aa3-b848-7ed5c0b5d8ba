diff --git a/drivers/spi/spi-imx.c b/drivers/spi/spi-imx.c
index 3cebf6b123fc..fixed 100644
--- a/drivers/spi/spi-imx.c
+++ b/drivers/spi/spi-imx.c
@@ -35,6 +35,7 @@
 static void spi_imx_configure_burst_length(struct spi_imx_data *spi_imx,
 					   struct spi_transfer *transfer)
 {
 	u32 ctrl;
 	unsigned int burst_length_bits;
+	
+	/* Debug: Print transfer configuration */
+	dev_info(spi_imx->dev, "Transfer config: len=%d, bits_per_word=%d\n",
+		 transfer->len, transfer->bits_per_word);
 
 	/* Calculate burst length in bits based on transfer size */
 	if (transfer->len >= 1024) {
@@ -57,6 +60,10 @@ static void spi_imx_configure_burst_length(struct spi_imx_data *spi_imx,
 	/* Hardware limit is 4096 bits (BURST_LENGTH max value 4095+1) */
 	burst_length_bits = min(burst_length_bits, 4096U);
 
+	/* Debug: Print burst configuration */
+	dev_info(spi_imx->dev, "Burst config: burst_bits=%d (%d bytes)\n",
+		 burst_length_bits, burst_length_bits / 8);
+
 	/* Configure burst length in CONREG */
 	ctrl = readl(spi_imx->base + MXC_CSPICTRL);
 	ctrl &= ~MXC_CSPICTRL_BL_MASK;
@@ -110,6 +117,10 @@ static int spi_imx_dma_configure(struct spi_master *master)
 
 	printk("bytes_per_word %d, wml %d \n", buswidth, spi_imx->wml);
 
+	/* Debug: Print DMA configuration details */
+	dev_info(spi_imx->dev, "DMA config: buswidth=%d, wml=%d, maxburst_tx=%d, maxburst_rx=%d\n",
+		 buswidth, spi_imx->wml, min(spi_imx->wml, 8U), min(spi_imx->wml, 8U));
+
 	tx.direction = DMA_MEM_TO_DEV;
 	tx.dst_addr = spi_imx->base_phys + MXC_CSPITXDATA;
 	tx.dst_addr_width = buswidth;
@@ -1421,6 +1432,16 @@ static int spi_imx_dma_transfer(struct spi_imx_data *spi_imx,
 	spi_imx->wml =  i;
 
+	/* Debug: Print WML calculation details */
+	bytes_per_word = spi_imx_bytes_per_word(transfer->bits_per_word);
+	dev_info(spi_imx->dev, "WML calculation: fifo_size=%d, bytes_per_word=%d, calculated_wml=%d\n",
+		 spi_imx->devtype_data->fifo_size, bytes_per_word, spi_imx->wml);
+
+	/* Debug: Print first few bytes of transfer data */
+	if (transfer->tx_buf) {
+		u8 *tx_buf = (u8 *)transfer->tx_buf;
+		dev_info(spi_imx->dev, "TX data: %02x %02x %02x %02x %02x %02x %02x %02x\n",
+			 tx_buf[0], tx_buf[1], tx_buf[2], tx_buf[3], tx_buf[4], tx_buf[5], tx_buf[6], tx_buf[7]);
+	}
+
 	ret = spi_imx_dma_configure(master);
 	if (ret)
 		return ret;
@@ -1471,6 +1492,13 @@ static int spi_imx_dma_transfer(struct spi_imx_data *spi_imx,
 
 	spi_imx->devtype_data->trigger(spi_imx);
 
+	/* Debug: Print final register values */
+	dev_info(spi_imx->dev, "Final registers: CONREG=0x%08x, DMAREG=0x%08x\n",
+		 readl(spi_imx->base + MXC_CSPICTRL),
+		 readl(spi_imx->base + MXC_CSPIDMAREG));
+	dev_info(spi_imx->dev, "CONREG decode: BURST_LENGTH=%d, SMC=%d\n",
+		 ((readl(spi_imx->base + MXC_CSPICTRL) & MXC_CSPICTRL_BL_MASK) >> MXC_CSPICTRL_BL_OFFSET) + 1,
+		 !!(readl(spi_imx->base + MXC_CSPICTRL) & MX51_ECSPI_CTRL_SMC));
+
 	/* Wait SDMA to finish the data transfer.*/
 	timeout = wait_for_completion_timeout(&spi_imx->dma_tx_completion,
 						transfer_timeout);
