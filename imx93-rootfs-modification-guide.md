# 🔧 NXP i.MX93 根文件系统修改指南

## 📖 前言

在嵌入式Linux开发中，经常需要对官方提供的根文件系统进行定制化修改，以满足特定的应用需求。传统的Yocto构建方式虽然功能强大，但学习曲线陡峭，编译时间长，对于简单的文件系统修改显得过于复杂。

本指南提供了一种**轻量级、高效率**的解决方案，让您能够：
- 🚀 **快速上手**：无需深入学习Yocto复杂的构建系统
- ⚡ **高效修改**：直接操作文件系统，所见即所得
- 🎯 **精确控制**：完全掌控每一个修改细节
- 🔄 **快速迭代**：修改-打包-测试的循环周期大大缩短

无论您是嵌入式新手还是资深开发者，这个方法都能让您在几分钟内完成根文件系统的定制化修改，告别漫长的编译等待时间！

## 📋 目录

- [整体流程图](#整体流程图)
- [环境准备](#环境准备)
- [解压根文件系统](#解压根文件系统)
- [修改根文件系统](#修改根文件系统)
- [清理工作目录](#清理工作目录)
- [重新打包](#重新打包)
- [部署验证](#部署验证)

## 🔄 整体流程图

```mermaid
flowchart LR
    A[📦 官方zst镜像<br/>zstd -d + tar -xpf<br/>直接解压到根文件系统] --> B[🎨 修改根文件系统<br/>添加应用/配置服务]
    B --> C[🧹 清理优化<br/>删除临时文件]
    C --> D[📦 重新打包<br/>tar -cpf + zstd压缩]
    D --> E[🚀 部署验证<br/>UUU工具烧录]

    style A fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    style B fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px
    style C fill:#fff3e0,stroke:#f57c00,stroke-width:3px
    style D fill:#e8f5e8,stroke:#388e3c,stroke-width:3px
    style E fill:#ffebee,stroke:#d32f2f,stroke-width:3px
```

整个修改过程分为5个简单步骤，每个步骤都有明确的目标，让您轻松掌握根文件系统的定制化修改。

## 🛠️ 环境准备

### 安装必要工具

```bash
# 安装核心工具
sudo apt update
sudo apt install -y zstd tar bzip2 e2fsprogs tree
```

### 创建工作目录

```bash
# 创建工作目录
mkdir -p ~/imx93_work
cd ~/imx93_work
```

## 📦 解压根文件系统

### 解压官方镜像

```bash
# 解压tar.zst文件（保持完整的权限和所有者信息）
zstd -d imx-image-full-imx93evk.tar.zst
sudo tar -xpf imx-image-full-imx93evk.tar --numeric-owner --same-owner

# 查看解压内容（应该直接是根文件系统结构）
ls -la
```

**解压后的目录结构：**
```
📁 当前目录/
├── 📂 bin/          # 基本命令
├── 📂 etc/          # 配置文件
├── 📂 usr/          # 用户程序
├── 📂 var/          # 变量数据
├── 📂 lib/          # 系统库
├── 📂 dev/          # 设备文件
└── 📂 ...           # 其他系统目录
```

### 验证解压结果

```bash
# 检查关键系统文件权限
ls -la etc/passwd etc/shadow
ls -la usr/bin/sudo

# 检查设备文件
ls -la dev/ | head -5

# 检查systemd服务文件
ls -la usr/lib/systemd/system/systemd-logind.service
ls -la usr/lib/systemd/system/systemd-timesyncd.service

# 检查重要目录权限
ls -ld var/log tmp run
```

## 🎨 修改根文件系统

### 准备修改环境

```bash
# 当前目录就是根文件系统，无需切换目录
# 直接开始修改操作
pwd  # 确认当前位置
```

### 常见修改操作

#### 1. 添加自定义应用程序

```bash
# 创建应用程序目录
sudo mkdir -p usr/local/bin

# 复制应用程序文件
sudo cp /path/to/your/app usr/local/bin/

# 设置权限
sudo chmod +x usr/local/bin/your-app
```

#### 2. 修改系统配置

```bash
# 修改SSH配置允许root登录
sudo sed -i 's/#PermitRootLogin.*/PermitRootLogin yes/' etc/ssh/sshd_config

# 设置时区为中国标准时间
# 检查时区数据是否存在
if [ -f usr/share/zoneinfo/Asia/Shanghai ]; then
    sudo ln -sf ../usr/share/zoneinfo/Asia/Shanghai etc/localtime
    echo "Asia/Shanghai" | sudo tee etc/timezone
    echo "✅ 时区设置完成"
else
    echo "⚠️ 时区数据不存在，将创建启动时设置服务"
    # 创建启动时设置时区的服务
    sudo mkdir -p etc/systemd/system
    cat | sudo tee etc/systemd/system/set-timezone.service << 'EOF'
[Unit]
Description=Set System Timezone to Asia/Shanghai
Before=systemd-timesyncd.service

[Service]
Type=oneshot
ExecStart=/bin/bash -c 'if [ -f /usr/share/zoneinfo/Asia/Shanghai ]; then ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && echo "Asia/Shanghai" > /etc/timezone; fi'
RemainAfterExit=yes

[Install]
WantedBy=sysinit.target
EOF

    # 启用服务
    sudo mkdir -p etc/systemd/system/sysinit.target.wants
    sudo ln -sf ../set-timezone.service etc/systemd/system/sysinit.target.wants/
fi

# 配置自动挂载U盘
sudo mkdir -p etc/udev/rules.d
cat | sudo tee etc/udev/rules.d/99-usb-automount.rules << 'EOF'
# 自动挂载U盘到/media目录
KERNEL=="sd[a-z][0-9]", SUBSYSTEM=="block", ACTION=="add", RUN+="/bin/mkdir -p /media/%k", RUN+="/bin/mount /dev/%k /media/%k"
KERNEL=="sd[a-z][0-9]", SUBSYSTEM=="block", ACTION=="remove", RUN+="/bin/umount /media/%k", RUN+="/bin/rmdir /media/%k"
EOF

# 修改系统欢迎信息
cat | sudo tee etc/motd << 'EOF'
 ___ __  __ _  _  ___  ___
|_ _|  \/  | \| ||   \|_  |
 | || |\/| |    || |) |/ /
|___|_|  |_|_|\_||___//___|

欢迎使用定制化i.MX93系统！
系统已针对您的应用需求进行优化配置。
EOF
```

#### 3. 添加系统服务

```bash
# 创建自定义服务
sudo mkdir -p etc/systemd/system
cat | sudo tee etc/systemd/system/custom-app.service << 'EOF'
[Unit]
Description=Custom Application Service
After=network.target

[Service]
Type=simple
ExecStart=/usr/local/bin/your-app
Restart=always
User=root

[Install]
WantedBy=multi-user.target
EOF

# 启用服务
sudo mkdir -p etc/systemd/system/multi-user.target.wants
sudo ln -sf ../custom-app.service etc/systemd/system/multi-user.target.wants/
```

#### 4. 添加用户和权限

```bash
# 添加用户组
echo "customgroup:x:1001:" | sudo tee -a etc/group

# 配置sudo权限
sudo mkdir -p etc/sudoers.d
echo "customuser ALL=(ALL) NOPASSWD:ALL" | sudo tee etc/sudoers.d/customuser
```

#### 5. 添加库文件

```bash
# 添加自定义库路径
echo "/usr/local/lib" | sudo tee -a etc/ld.so.conf.d/custom.conf
```

### 验证修改

```bash
# 检查文件系统大小
sudo du -sh .

# 检查关键目录
ls -la bin sbin usr/bin usr/sbin lib usr/lib etc

# 检查自定义文件
ls -la usr/local/bin/
ls -la etc/systemd/system/custom-*
```

## 🧹 清理工作目录

在重新打包之前，清理不必要的临时文件可以减小最终镜像的大小：

```bash
# 清理包管理器缓存
sudo rm -rf var/cache/apt/archives/*.deb
sudo rm -rf var/lib/apt/lists/*

# 清理日志文件
sudo rm -rf var/log/*
sudo mkdir -p var/log

# 清理临时文件
sudo rm -rf tmp/*
sudo rm -rf var/tmp/*

# 清理用户历史记录
sudo rm -f root/.bash_history
sudo rm -f home/*/.bash_history

# 清理SSH主机密钥（首次启动时重新生成）
sudo rm -f etc/ssh/ssh_host_*

# 检查清理效果
echo "清理前后大小对比："
sudo du -sh .
```

## 📦 重新打包

### 重新打包根文件系统

```bash
# 重新打包为tar文件（使用通配符避免包含tar文件本身）
sudo tar -cpf imx-image-full-imx93evk.tar.new * --numeric-owner --same-owner

# 压缩为zst格式（使用sudo确保权限）
sudo zstd -19 -T0 imx-image-full-imx93evk.tar.new -o imx-image-full-imx93evk.tar.zst.new

# 验证打包结果
ls -lh imx-image-full-imx93evk.tar.zst*

# 清理临时文件
sudo rm imx-image-full-imx93evk.tar.new
```

### 验证打包结果

```bash
# 验证zst文件完整性
zstd -t imx-image-full-imx93evk.tar.zst.new

# 查看文件大小
ls -lh imx-image-full-imx93evk.tar.zst.new

# 生成校验和
sha256sum imx-image-full-imx93evk.tar.zst.new > imx-image-full-imx93evk.tar.zst.new.sha256
```

## ⚠️ 重要注意事项

### 关键参数说明

1. **完整权限保持**：
   - `-p` 或 `--preserve-permissions`：保持文件权限
   - `--numeric-owner`：保持数字形式的用户ID和组ID
   - `--same-owner`：保持原始所有者信息

2. **文件完整性**：
   - 使用`sudo`确保有足够权限操作所有文件
   - 避免在普通用户权限下操作设备文件
   - 保持符号链接的完整性

3. **系统文件检查**：
   ```bash
   # 检查关键系统文件权限是否正确
   ls -la etc/passwd etc/shadow
   ls -la usr/bin/sudo
   ls -la bin/su
   ```

4. **设备文件验证**：
   ```bash
   # 检查设备目录（现代系统通常为空或只有基本节点）
   ls -la dev/

   # 检查udev服务文件（负责动态设备管理）
   ls -la usr/lib/systemd/system/systemd-udevd.service

   # 检查udev规则（设备管理规则）
   ls -la etc/udev/rules.d/
   ls -la usr/lib/udev/rules.d/
   ```

5. **设备管理说明**：
   - **现代Linux系统**：使用devtmpfs + udev动态管理设备
   - **无需手动创建**：设备节点由内核和udev自动创建
   - **rootfs中的/dev**：通常为空目录或只有基本节点
   - **设备规则**：通过udev规则文件定制设备行为

### 安全检查

```bash
# 检查SUID文件
find . -type f \( -perm -4000 -o -perm -2000 \) -ls

# 检查世界可写文件
find . -type f -perm -002 -ls

# 检查空密码账户
awk -F: '($2 == "" || $2 == "!") {print $1}' etc/shadow
```

## 🚀 部署验证

### 部署方法

#### 使用UUU工具部署到eMMC

```bash
# 使用UUU工具烧录（推荐）
sudo uuu -b emmc_all imx-boot-imx93evk-sd.bin-flash_evk imx-image-full-imx93evk.tar.zst.new
```

#### 烧录到SD卡

```bash
# 解压并提取WIC文件
zstd -d imx-image-full-imx93evk.tar.zst.new -o extracted.tar
tar -xf extracted.tar

# 烧录WIC镜像到SD卡
sudo dd if=imx-image-full-imx93evk.rootfs.wic of=/dev/sdX bs=1M status=progress
sync
```

### 设备验证

启动设备后，通过串口或SSH连接验证修改：

```bash
# 检查自定义文件
ls -la /usr/local/bin/

# 检查系统服务
systemctl status custom-app

# 检查主机名
hostname

# 检查网络配置
ip addr show

# 检查系统信息
cat /etc/os-release
```

### 最终清理

```bash
# 清理工作目录中的临时文件
rm -f imx-image-full-imx93evk.tar
rm -f *.tmp

# 当前目录就是根文件系统，可以保留供后续修改使用
echo "✅ 部署完成，当前目录的根文件系统已保留供后续修改使用"
```

## 📚 总结

本指南提供了简洁的i.MX93根文件系统修改方案：

- ✅ **简单易用**：只需几个核心命令
- ✅ **完全独立**：不依赖Yocto构建环境
- ✅ **安全可靠**：保持文件权限和完整性
- ✅ **灵活扩展**：支持各种自定义修改需求

通过本指南，您可以快速对NXP i.MX93的根文件系统进行定制化修改。

> **相关文档**：如需了解WIC镜像的创建和处理，请参考《NXP i.MX93 WIC镜像制作完全指南》。
