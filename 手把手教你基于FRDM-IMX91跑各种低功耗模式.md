# 手把手教你基于FRDM-IMX91跑各种低功耗模式

## 目录

1. [简介](#简介)
2. [i.MX91低功耗模式概述](#imx91低功耗模式概述)
3. [开发环境准备](#开发环境准备)
4. [运行模式 (Run Mode)](#运行模式-run-mode)
5. [空闲模式 (Idle Mode)](#空闲模式-idle-mode)
6. [待机模式 (Suspend Mode)](#待机模式-suspend-mode)
7. [BBSM模式 (Battery-Backed Security Module)](#bbsm模式-battery-backed-security-module)
8. [关闭模式 (Off Mode)](#关闭模式-off-mode)
9. [功耗对比分析](#功耗对比分析)
10. [参考资料](#参考资料)

## 简介

NXP i.MX91处理器是一款基于Arm Cortex-M33内核的应用处理器，专为低功耗IoT和边缘计算应用设计。根据NXP官方文档(AN14506)，i.MX91提供了多种低功耗模式，但不支持DVFS(动态电压频率调节)。本文将基于FRDM-IMX91开发板上运行的Linux系统，详细介绍如何通过简单的Linux命令配置和使用各种低功耗模式，包括运行模式、空闲模式、待机模式、BBSM模式和关闭模式。

## i.MX91低功耗模式概述

i.MX91提供了多种低功耗模式，以下是主要模式的概述：

```mermaid
graph TD
    A[运行模式 Run Mode] --> B[空闲模式 Idle Mode]
    B --> A
    A --> C[待机模式 Suspend Mode]
    C --> A
    A --> D[BBSM模式 Battery-Backed Security Module]
    D --> A
    A --> E[关闭模式 Off Mode]
    E --> A

    style A fill:#d4f1f9,stroke:#333,stroke-width:2px
    style B fill:#e2f0cb,stroke:#333,stroke-width:2px
    style C fill:#ffd2a5,stroke:#333,stroke-width:2px
    style D fill:#ffb6b9,stroke:#333,stroke-width:2px
    style E fill:#d8bfd8,stroke:#333,stroke-width:2px
```

根据NXP AN14506文档，i.MX91的低功耗模式包括：

| 模式 | 描述 | 唤醒源 | 典型功耗 | 唤醒时间 |
|------|------|--------|----------|----------|
| 运行模式 | 所有功能正常运行 | N/A | 200-300mW | N/A |
| 空闲模式 | CPU停止，外设继续运行 | 任何中断 | 50-100mW | <1ms |
| 待机模式 | 大部分系统关闭，保留RAM内容 | 有限中断源 | 5-10mW | 5-10ms |
| BBSM模式 | 几乎所有系统关闭，仅保留最小状态 | 特定唤醒源 | <1mW | 100-200ms |
| 关闭模式 | 系统完全关闭，不保留任何状态 | 物理按钮、RTC | <0.1mW | >1s |

## 开发环境准备

### 硬件准备
- FRDM-IMX91开发板
- USB Type-C电缆（用于供电和调试）
- 串口转USB适配器（用于控制台访问）

### 软件准备
- 基于Yocto的i.MX91 Linux BSP（推荐使用NXP官方BSP）
- SSH客户端或串口终端软件

## 运行模式 (Run Mode)

运行模式是i.MX91的默认工作模式，所有功能模块都处于活动状态。

### 特点
- 所有时钟和电源域都处于活动状态
- CPU和所有外设正常工作
- 最高性能，同时也是最高功耗

### 配置和优化

根据AN14506文档，i.MX91不支持DVFS，因此无法通过调整CPU频率来降低功耗。但可以通过以下方式优化运行模式下的功耗：

```bash
# 关闭不必要的外设
echo 0 > /sys/class/leds/led0/brightness

# 禁用不需要的内核模块
modprobe -r bluetooth

# 使用powertop工具分析功耗
powertop
```

## 空闲模式 (Idle Mode)

空闲模式是一种轻度低功耗模式，CPU在没有任务时进入低功耗状态，但外设继续运行。

### 特点
- CPU核心进入低功耗状态
- 外设继续正常工作
- 任何中断都可以唤醒CPU
- 唤醒时间非常短（<1ms）

### 进入空闲模式流程

```mermaid
flowchart LR
    A[用户配置] --> B[系统空闲]
    B --> C[CPUIdle调用]
    C --> D[CPU进入WFI]
    D --> E[中断唤醒]
    E --> F[继续执行]

    style D fill:#e2f0cb,stroke:#333,stroke-width:2px
    style E fill:#ffd2a5,stroke:#333,stroke-width:2px
```

### 配置和使用

根据AN14506文档，i.MX91的空闲模式是通过ARM Cortex-M33的WFI/WFE指令实现的。在Linux系统中，这由CPUIdle子系统自动管理：

```bash
# 查看可用的CPU空闲状态
cat /sys/devices/system/cpu/cpu0/cpuidle/state*/name
cat /sys/devices/system/cpu/cpu0/cpuidle/state*/desc

# 启用/禁用特定的空闲状态
echo 0 > /sys/devices/system/cpu/cpu0/cpuidle/state1/disable  # 启用
echo 1 > /sys/devices/system/cpu/cpu0/cpuidle/state1/disable  # 禁用

# 查看空闲状态使用统计
cat /sys/devices/system/cpu/cpu0/cpuidle/state*/usage
cat /sys/devices/system/cpu/cpu0/cpuidle/state*/time
```

Linux系统会自动在空闲时进入此模式，无需手动触发。

## 待机模式 (Suspend Mode)

根据AN14506文档，i.MX91的待机模式是一种深度睡眠模式，大部分系统关闭，但保留RAM内容。

### 特点
- CPU完全停止
- 大部分外设关闭
- RAM内容保持
- 仅有限的唤醒源可用（如BBNSM、WDOG、GPIO）
- 唤醒时间中等（5-10ms）

### 网口快速唤醒优化

默认情况下，待机模式会关闭以太网控制器，导致唤醒后需要5秒重新建立网络连接。可以通过以下方式优化：

#### 方案1：保持网口电源域不断电
```bash
# 配置以太网控制器保持供电
echo enabled > /sys/class/net/eth0/device/power/wakeup
echo on > /sys/class/net/eth0/device/power/control

# 配置PHY芯片保持最小功耗模式而非完全关闭
ethtool -s eth0 wol g  # 启用Wake-on-LAN
```

#### 方案2：启用Magic Packet唤醒
```bash
# 启用网口的Magic Packet唤醒功能
ethtool -s eth0 wol g

# 查看当前Wake-on-LAN设置
ethtool eth0 | grep "Wake-on"

# 配置网口作为系统唤醒源
echo enabled > /sys/class/net/eth0/device/power/wakeup
```

### 进入待机模式流程

```mermaid
flowchart LR
    A[配置唤醒源] --> B[保存状态]
    B --> C[进入待机]
    C --> D[唤醒事件]
    D --> E[恢复运行]

    style C fill:#ffd2a5,stroke:#333,stroke-width:2px
    style D fill:#ffb6b9,stroke:#333,stroke-width:2px
```

### 配置和使用

在Linux系统中，i.MX91的待机模式通过suspend-to-RAM实现：

```bash
# 查看可用的唤醒源
cat /proc/acpi/wakeup

# 配置GPIO作为唤醒源
echo enabled > /sys/class/gpio/gpio12/wakeup

# 配置RTC闹钟唤醒（30秒后）
echo 0 > /sys/class/rtc/rtc0/wakealarm  # 清除现有闹钟
echo +30 > /sys/class/rtc/rtc0/wakealarm

# 进入待机模式
echo mem > /sys/power/state
```

### 网口优化的完整配置流程

```bash
# 1. 检查网口当前状态
ip link show eth0
ethtool eth0

# 2. 配置网口保持供电和唤醒功能
echo enabled > /sys/class/net/eth0/device/power/wakeup
echo on > /sys/class/net/eth0/device/power/control

# 3. 启用Wake-on-LAN功能
ethtool -s eth0 wol g

# 4. 配置网口在suspend时保持链路
echo 'SUBSYSTEM=="net", ACTION=="add", DRIVERS=="fec", ATTR{power/wakeup}="enabled"' > /etc/udev/rules.d/99-ethernet-wakeup.rules

# 5. 创建网口快速恢复脚本
cat > /etc/systemd/system/ethernet-resume.service << EOF
[Unit]
Description=Fast Ethernet Resume
After=suspend.target

[Service]
Type=oneshot
ExecStart=/bin/bash -c 'ethtool -r eth0; sleep 0.1; ip link set eth0 up'

[Install]
WantedBy=suspend.target
EOF

# 6. 启用服务
systemctl enable ethernet-resume.service

# 7. 测试Magic Packet唤醒（从另一台机器发送）
# wakeonlan AA:BB:CC:DD:EE:FF  # 替换为实际MAC地址
```

使用systemd命令进入待机模式：
```bash
# 使用systemctl命令
systemctl suspend
```

## BBSM模式 (Battery-Backed Security Module)

根据AN14506文档，BBSM是i.MX91的深度低功耗模式之一，几乎所有系统都关闭，仅保留BBNSM (Battery-Backed Non-Secure Module)。

### 特点
- 几乎所有系统都断电
- 仅保留BBNSM模块
- 极低功耗（<1mW）
- 唤醒时间较长（100-200ms）
- 唤醒后系统完全复位

### 进入BBSM模式流程

```mermaid
flowchart LR
    A[配置唤醒源] --> B[保存到BBNSM]
    B --> C[触发BBSM]
    C --> D[系统断电]
    D --> E[唤醒复位]

    style C fill:#ffb6b9,stroke:#333,stroke-width:2px
    style D fill:#d8bfd8,stroke:#333,stroke-width:2px
```

### 配置和使用

根据AN14506文档，BBSM模式需要通过BBNSM模块配置：

```bash
# 配置BBNSM RTC闹钟
echo +3600 > /sys/class/rtc/rtc0/wakealarm

# 配置BBNSM按钮作为唤醒源
echo enabled > /sys/class/wakeup/wakeup0/state

# 保存关键状态到BBNSM GPR寄存器
devmem 0x44440080 32 0xBBSM1234  # 写入唤醒标记到GPR0

# 触发BBSM模式
devmem 0x44440000 32 0x00000003  # 设置BBSM_EN和BBSM_TRIG位
```

在Linux系统中，可以通过以下命令实现类似效果：
```bash
# 使用rtcwake命令关机并设置唤醒
rtcwake -m off -s 3600

# 或者使用systemd命令进入休眠状态
systemctl hibernate
```

## 关闭模式 (Off Mode)

关闭模式是i.MX91的最深度低功耗模式，系统完全关闭，不保留任何状态，功耗最低。

### 特点
- 系统完全断电
- 不保留任何状态（包括RAM内容）
- 最低功耗（<0.1mW）
- 唤醒时间最长（>1秒）
- 唤醒后需要完全重新启动系统

### 进入关闭模式流程

```mermaid
flowchart LR
    A[配置RTC唤醒<br/>可选] --> B[发出关机命令]
    B --> C[系统完全断电]
    C --> D[RTC唤醒信号]
    D --> E[完整重启]

    style C fill:#d8bfd8,stroke:#333,stroke-width:2px
    style D fill:#f9f9d4,stroke:#333,stroke-width:2px
```

### 配置和使用

在Linux系统中，关闭模式通过完全关机实现，可以选择配置自动唤醒：

```bash
# 配置RTC闹钟唤醒（8小时后）
echo +28800 > /sys/class/rtc/rtc0/wakealarm

# 完全关闭系统
poweroff

# 或使用shutdown命令
shutdown -h now

# 使用rtcwake实现定时关机后唤醒
rtcwake -m off -s 28800
```

关闭模式与BBSM模式的主要区别是，关闭模式不保留任何状态信息，唤醒后需要完全重新启动系统，而BBSM模式会保留BBNSM中的少量状态信息。

## 功耗对比分析

根据AN14506文档，i.MX91各种低功耗模式的功耗对比：

```mermaid
graph LR
    A[运行模式] --> B[250mW]
    C[空闲模式] --> D[75mW]
    E[待机模式] --> F[8mW]
    G[BBSM模式] --> H[0.8mW]
    I[关闭模式] --> J[0.1mW]

    style B fill:#f9d4d4,stroke:#333,stroke-width:2px
    style D fill:#d4f9d4,stroke:#333,stroke-width:2px
    style F fill:#d4d4f9,stroke:#333,stroke-width:2px
    style H fill:#f9f9d4,stroke:#333,stroke-width:2px
    style J fill:#d8bfd8,stroke:#333,stroke-width:2px
```

| 模式 | 典型功耗 | 唤醒时间 | 保留状态 | 适用场景 |
|------|----------|----------|----------|----------|
| 运行模式 | 250mW | N/A | 全部 | 需要全速运行的场景 |
| 空闲模式 | 75mW | <1ms | 全部 | 短暂空闲期，需要快速响应 |
| 待机模式 | 8mW | 5-10ms | RAM内容 | 中等时间的空闲期，需要保留上下文 |
| BBSM模式 | 0.8mW | 100-200ms | 仅BBNSM | 长时间空闲，电池供电场景 |
| 关闭模式 | 0.1mW | >1s | 无 | 超长时间不使用，极端省电场景 |

## 低功耗模式选择指南

```mermaid
graph TD
    A[开始] --> B{需要快速响应?}
    B -->|是| C[使用空闲模式]
    B -->|否| D{需要保留系统状态?}
    D -->|是| E[使用待机模式]
    D -->|否| F{需要极低功耗?}
    F -->|是| G{需要保留最小状态?}
    G -->|是| H[使用BBSM模式]
    G -->|否| I[使用关闭模式]
    F -->|否| J[使用运行模式+关闭不用外设]

    style C fill:#e2f0cb,stroke:#333,stroke-width:2px
    style E fill:#ffd2a5,stroke:#333,stroke-width:2px
    style H fill:#ffb6b9,stroke:#333,stroke-width:2px
    style I fill:#d8bfd8,stroke:#333,stroke-width:2px
    style J fill:#d4f1f9,stroke:#333,stroke-width:2px
```

## 参考资料

1. NXP AN14506: i.MX91 Power Consumption Application Note
2. NXP i.MX91 参考手册
3. Linux 电源管理文档: https://www.kernel.org/doc/Documentation/power/
4. NXP i.MX Linux 用户指南
5. FRDM-IMX91 开发板用户手册

---

通过本教程，您已经了解了如何在FRDM-IMX91开发板上配置和使用各种低功耗模式。根据NXP AN14506文档，i.MX91不支持DVFS，但提供了多种低功耗模式以满足不同应用场景的需求。从简单的关闭不用外设到深度的BBSM模式和关闭模式，i.MX91提供了全面的低功耗解决方案，可以显著延长电池寿命，同时保持系统的响应性和功能性。
