uboot-imx/doc/imx/habv4/guides/mx6_mx7_spl_secure_boot.txt at lf_v2025.04 · nxp-imx/uboot-imx · GitHub
一 CST工具生成秘钥
1 下载CST
https://www.nxp.com/webapp/sps/download/license.jsp?colCode=IMX_CST_TOOL&appType=file2&location=null&DOWNLOAD_ID=null&lang_cd=en
 
2 生成PKI tree
  下载之后解压进入目录执行脚本
 	 
Notice: 
我们可以在crts目录中找到公钥并在keys目录中找到私钥
 
3 生成SRK及Hash值
 
这里生成的 SRK_1_2_3_4_table.bin 为SRK Table，将后续的步骤中用于对Container进行签名。SRK_1_2_3_4_fuse.bin 为SRK 的Hash值，将用于后续芯片的fuse烧写。
二 启动镜像编译和签名
1 交叉编译工具链
交叉编译工具链：arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-linux-gnueabihf.tar.xz(https://developer.arm.com/downloads/-/arm-gnu-toolchain-downloads)
 
2 uboot
1 下载
wget https://github.com/nxp-imx/uboot-imx/archive/refs/tags/lf-6.1.22-2.0.0.tar.gz -O uboot-lf-6.1.22-2.0.0.tar.gz
 
2 配置
进入uboot：修改.config
    CONFIG_IMX_HAB=Y
3 编译
make mx6ull_14x14_evk_nand_defconifg
make 
将u-boot-dtb.imx;u-boot-dtb.imx.log 
doc/imx/habv4/csf_examples/mx6_mx7/csf_uboot.txt全部拷贝到cst-3.1
2 uboot签名
1 修改csf_uboot.txt
查看u-boot-dtb.imx.log 文件内容
 
修改csf_uboot.txt如下图所示相应位置参数
 
2 签名生成
cjx/cst-3.1.0/release$ ./linux64/bin/cst -i csf_uboot.txt -o csf_uboot.bin
CSF Processed successfully and signed data available in csf_uboot.bin
cjx@cjx-VirtualBox:~/test-cjx/cst-3.1.0/release$ cat u-boot-dtb.imx csf_uboot.bin  > u-boot-signed.imx 
注意一下存储在nandflash才进行操作
cjx@cjx-VirtualBox:~/test-cjx/cst-3.1.0/release$ od -x -N 48 u-boot-dtb.imx
0000000 00d1 4020 0000 8780 0000 0000 f42c 877f
0000020 f420 877f f400 877f 7000 878a 0000 0000
0000040 f000 877f a060 000a 0000 0000 01d2 40e8
0000060
cjx@cjx-VirtualBox:~/test-cjx/cst-3.1.0/release$ objcopy -I binary -O binary --pad-to 0xaa060 --gap-fill=0xff u-boot-signed.imx u-boot-signed-pad.imx
（3.6 For NAND boot, since boot_data.size is larger than uboot image length, and ROM will read boot_data.size from NAND flash, if skip the last step in "3.3 The command to sign u-boot", ROM can't read uboot from NAND flash correctly, the bootup will fail.
）
三 Secure Boot启动uboot操作
1 u-boot-signed.imx烧录
2 查看SRK Table的Hash值
查看我们生成的SRK Table的Hash值
hexdump -e '/4 ”@x”’ -e  '/4 “%X""\n"’ SRK_1_2_3_4_fuse.bin
(od -x -N 48 u-boot.imx)
 
2 烧录SRK Table的Hash值
启动开发板并在Uboot倒计时阶段打断启动，使用如下Uboot命令烧写fuse。The table below lists the SRK_HASH bank and word according to the i.MX device:

    +-------------------+---------------+---------------+---------------+
    |                   |  i.MX6 Series |    i.MX7D/S   |    i.MX7ULP   |
    +-------------------+---------------+---------------+---------------+
    | SRK_HASH[31:00]   | bank 3 word 0 | bank 6 word 0 | bank 5 word 0 |
    +-------------------+---------------+---------------+---------------+
    | SRK_HASH[63:32]   | bank 3 word 1 | bank 6 word 1 | bank 5 word 1 |
    +-------------------+---------------+---------------+---------------+
    | SRK_HASH[95:64]   | bank 3 word 2 | bank 6 word 2 | bank 5 word 2 |
    +-------------------+---------------+---------------+---------------+
    | SRK_HASH[127:96]  | bank 3 word 3 | bank 6 word 3 | bank 5 word 3 |
    +-------------------+---------------+---------------+---------------+
    | SRK_HASH[159:128] | bank 3 word 4 | bank 7 word 0 | bank 5 word 4 |
    +-------------------+---------------+---------------+---------------+
    | SRK_HASH[191:160] | bank 3 word 5 | bank 7 word 1 | bank 5 word 5 |
    +-------------------+---------------+---------------+---------------+
    | SRK_HASH[223:192] | bank 3 word 6 | bank 7 word 2 | bank 5 word 6 |
    +-------------------+---------------+---------------+---------------+
    | SRK_HASH[255:224] | bank 3 word 7 | bank 7 word 3 | bank 5 word 7 |
    +-------------------+---------------+---------------+---------------+
	进入uboot之后命令模式
 
fuse prog 3 0 0x99222FD9
fuse prog 3 1 0xA654EE5D
fuse prog 3 2 0xF8AD642F
fuse prog 3 3 0x2EF63C7D
fuse prog 3 4 0xE3A184FF
fuse prog 3 5 0xFCFC1D33
fuse prog 3 6 0xC8769CF8
fuse prog 3 7 0xB08FE6BC
查看烧录结果：fuse read 3 0 8( fuse read <bank> <word> [<cnt>] )
 
使用 ahab_status 命令查看AHAB状态，这里需要确保 “No Events Found!”
 
这个命令会读取ELE中的Log，如果任意Container中使用的SRK Table的Hash值与fuse中保存的不符，都会有Events进行记录。这里我们可能会遇到关于不符情况的几种不同的status，需要根据提示修改启动镜像：
3 熔断fuse!!!!
在确认 ahab_status 状态后，执行 ahab_close(该芯片只能使用前面烧录hash值对应前面镜像启动)命令。Open状态下，我们同样可以使用未经签名或错误签名的启动镜像启动芯片。当我们设置close后，将只可以使用正确签名的启动镜像启动芯片及开发板
=> fuse prog 0 6 0x00000002

The table below list the SEC_CONFIG[1] bank and word according to the i.MX
device:

             +--------------+-----------------+------------+
             |    Device    |  Bank and Word  |    Value   |
             +--------------+-----------------+------------+
             | i.MX6 Series |  bank 0 word 6  | 0x00000002 |
             +--------------+-----------------+------------+
             | i.MX7D/S     |  bank 1 word 3  | 0x02000000 |
             +--------------+-----------------+------------+
             | i.MX7ULP     |  bank 29 word 6 | 0x80000000 |
             +--------------+-----------------+------------+

此时，芯片可以已 Secure Boot 的方式启动！！！
四 Secure Boot启动linux操作
1 镜像对齐
zImage 必须被填充到下一个边界地址（0x1000）处，例如
如果图像大小为 0x649920，那么它必须被填充到 0x64A000 处。
objcopy 这个工具可用于对图像进行填充操作。
- 填充 zImage：
$ objcopy -I binary -O binary --pad-to 0x64A000 --gap-fill=0x00  zImage zImage_pad.bin
2 镜像向量表
HAB 代码需要一个图像向量表（IVT）来确定图像长度和 CSF 位置。由于 zImage 不包含 IVT，所以必须手动创建并附加到填充后的 zImage 的末尾，script_examples(doc/imx/habv4/script_examples/genIVT.pl) 目录中的 genIVT.pl 脚本可供参考。
 
- 生成 IVT：
$ genIVT.pl  (执行生成ivt.bin)
注意：加载地址可能会因设备不同而有所变化。
在填充后的 zImage 文件末尾添加 ivt.bin 文件：
$ cat zImage_pad.bin ivt.bin > zImage_pad_ivt.bin
Load addres：uboot加载kernel到内存中的位置
Self Pointer: loadd_addres+ zImage_pad.bin（大小）
CSF Pointer: Self Pointer + 0X20（大小）

3 镜像签名
必须创建一个 CSF 文件来对图像进行签名。HAB 不允许在首次对图像进行认证后更改 SRK，因此在扩展信任根时必须使用 U-Boot 中使用的相同的 SRK 密钥。
CSF 示例文件可在 ../csf_examples/additional_images/ 目录中获取。
- 创建 CSF 二进制文件：
 
内核为zImage_pad_ivt.bin文件，设备树为原始文件，csf_zImage.bin 为通过签名加密的zImae_pad_ivt.bin和设备树木文件(dtb文件)。
$ ./cst --i csf_additional_images.txt --o csf_zImage.bin
- 将 CSF 二进制文件附加到图像的末尾：
$ cat zImage_pad_ivt.bin csf_zImage.bin > zImage_signed.bin
最终签名加密文件zImage_signed.bin为下图所示的结合 
4 签名镜像校验
U-Boot 包含 hab_auth_img 命令，该命令可用于对签名镜像进行认证和故障排查，而 zImage 必须按照 IVT 中指定的加载地址进行加载。
- 验证附加图像：
=> hab_auth_img <加载地址> <图像大小> <中断向量表偏移量>
zImage 成功完成签名。
 

nand read 0x80800000 0x500000 0x800000  //flash读取签名文件到内存
nand read 0x83000000 0x400000 0x100000  //设备树读原始文件到内存
hab_auth_img 0x80800000 0x66AF68 0x0066A000 //最终前面文件+16字节
 


