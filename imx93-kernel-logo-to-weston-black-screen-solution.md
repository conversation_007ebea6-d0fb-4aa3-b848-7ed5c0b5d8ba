# 🔧 i.MX93 Linux Logo到Weston桌面黑屏问题解决方案

## 📋 问题概述

**环境配置：**
- 处理器：NXP i.MX93
- BSP版本：L6.6.52_2.2.0
- 开发板：IMX93-EVK
- 显示配置：启用kernel logo，关闭Yocto splash，未启用U-Boot logo

**问题现象：**
在启动过程中，Linux kernel logo显示后到Weston桌面启动前出现短暂黑屏。

## 🔍 问题分析

### 启动流程分析

```mermaid
flowchart TD
    A[系统上电] --> B[U-Boot启动]
    B --> C{是否启用U-Boot logo?}
    C -->|否| D[屏幕黑屏]
    C -->|是| E[显示U-Boot logo]
    D --> F[Kernel启动]
    E --> F
    F --> G[显示Kernel Logo<br/>CONFIG_LOGO=y]
    G --> H[Kernel启动完成]
    H --> I{应用解决方案}

    I --> J[方案一：Kernel参数<br/>vt.global_cursor_default=0<br/>consoleblank=0]
    I --> K[方案二：Weston配置<br/>background-image设置]
    I --> L[方案三：Systemd优化<br/>framebuffer-keeper服务]
    I --> M[方案四：固定内存<br/>reserved-memory配置]

    J --> N[保持Framebuffer内容]
    K --> O[快速显示背景图片]
    L --> P[优化启动顺序]
    M --> Q[内存区域保护]

    N --> R[Weston启动]
    O --> R
    P --> R
    Q --> R

    R --> S[显示桌面]
    S --> T[启动完成<br/>无黑屏]

    style A fill:#e1f5fe
    style G fill:#c8e6c9
    style T fill:#c8e6c9
    style J fill:#fff3e0
    style K fill:#fff3e0
    style L fill:#fff3e0
    style M fill:#fff3e0
```

### 黑屏产生的根本原因

1. **Framebuffer清空**：kernel logo显示后，framebuffer内容被控制台或其他进程清空
2. **显示控制权转移**：从kernel framebuffer console转移到Weston的过程中存在空隙
3. **控制台光标和清屏**：虚拟终端(VT)系统可能清除framebuffer内容
4. **Weston初始化延迟**：Weston启动需要时间，在接管显示前出现空白

## 🛠️ 解决方案

### 方案一：保持Framebuffer内容不被清除（推荐）

#### 1.1 修改Kernel启动参数

在U-Boot环境变量或设备树中添加以下启动参数：

```bash
# 在U-Boot中设置启动参数
setenv bootargs "console=ttyLP0,115200 root=/dev/mmcblk1p2 rootwait rw \
    vt.global_cursor_default=0 \
    consoleblank=0 \
    quiet \
    logo.nologo=0"

# 保存环境变量
saveenv
```

**参数说明：**
- `vt.global_cursor_default=0`：禁用虚拟终端光标，避免光标闪烁清除framebuffer
- `consoleblank=0`：禁用控制台自动清屏功能
- `quiet`：减少启动信息输出，避免文字覆盖logo
- `logo.nologo=0`：确保kernel logo显示

#### 1.2 修改Kernel配置

确保以下kernel配置正确：

```bash
# 在kernel defconfig中确认以下配置
CONFIG_FRAMEBUFFER_CONSOLE=y
CONFIG_FRAMEBUFFER_CONSOLE_DETECT_PRIMARY=y
CONFIG_LOGO=y
CONFIG_LOGO_LINUX_CLUT224=y
CONFIG_VT=y
CONFIG_VT_CONSOLE=y
CONFIG_VT_HW_CONSOLE_BINDING=y

# 避免以下配置（可能导致framebuffer被清除）
# CONFIG_FRAMEBUFFER_CONSOLE_DEFERRED_TAKEOVER is not set
```

### 方案二：配置Weston快速启动和背景显示

#### 2.1 优化Weston配置

编辑`/etc/xdg/weston/weston.ini`：

```ini
[core]
# 使用DRM后端，更快的启动速度
backend=drm-backend.so
# 禁用空闲超时，保持显示活跃
idle-time=0

[shell]
# 设置背景图片，避免黑屏
background-image=/usr/share/backgrounds/kernel-logo.png
background-type=scale-crop
background-color=0xff000000

# 禁用锁屏，减少启动时间
locking=false

[output]
# 针对i.MX93 MIPI-DSI显示器配置
name=DSI-1
mode=1080x1920
transform=normal
```

#### 2.2 创建过渡背景图片

```bash
# 创建与kernel logo相同的背景图片
mkdir -p /usr/share/backgrounds/

# 将kernel logo转换为PNG格式作为Weston背景
# 假设kernel logo为企鹅图标，创建相同的背景
convert /usr/src/linux/drivers/video/logo/logo_linux_clut224.ppm \
    -resize 1080x1920 -gravity center -background black \
    -extent 1080x1920 /usr/share/backgrounds/kernel-logo.png
```

### 方案三：使用Systemd优化启动顺序

#### 3.1 创建Framebuffer保持服务

创建`/etc/systemd/system/framebuffer-keeper.service`：

```ini
[Unit]
Description=Keep framebuffer content during boot
DefaultDependencies=false
Before=weston.service
After=multi-user.target

[Service]
Type=oneshot
ExecStart=/bin/sh -c 'echo 0 > /sys/class/vtconsole/vtcon1/bind'
ExecStart=/bin/sh -c 'echo 0 > /proc/sys/kernel/printk'
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
```

#### 3.2 优化Weston启动服务

修改`/etc/systemd/system/weston.service`：

```ini
[Unit]
Description=Weston Wayland Compositor
After=framebuffer-keeper.service
Wants=framebuffer-keeper.service

[Service]
Type=notify
ExecStart=/usr/bin/weston --backend=drm-backend.so --log=/var/log/weston.log
User=root
Group=root
Environment=XDG_RUNTIME_DIR=/run/user/0
Environment=WAYLAND_DISPLAY=wayland-0

# 快速启动配置
TimeoutStartSec=10
Restart=on-failure
RestartSec=1

[Install]
WantedBy=graphical.target
```

### 方案四：固定Framebuffer内存区域（高级方案）

#### 4.1 修改设备树

在设备树中为framebuffer分配固定内存：

```dts
/ {
    reserved-memory {
        #address-cells = <2>;
        #size-cells = <2>;
        ranges;

        framebuffer_reserved: framebuffer@90000000 {
            compatible = "shared-dma-pool";
            reg = <0x0 0x90000000 0x0 0x00800000>; /* 8MB for framebuffer */
            no-map;
        };
    };
};

&lcdif {
    memory-region = <&framebuffer_reserved>;
    status = "okay";
};
```

#### 4.2 修改Kernel启动参数

```bash
# 添加framebuffer内存保护参数
setenv bootargs "console=ttyLP0,115200 root=/dev/mmcblk1p2 rootwait rw \
    vt.global_cursor_default=0 consoleblank=0 quiet \
    memmap=8M\$0x90000000 \
    video=HDMI-A-1:1080x1920@60"
```

## 🧪 测试验证

### 验证脚本

创建测试脚本`/usr/local/bin/boot-display-test.sh`：

```bash
#!/bin/bash
# 启动显示测试脚本

echo "=== i.MX93 启动显示测试 ==="

# 检查framebuffer状态
echo "1. Framebuffer设备状态："
ls -la /dev/fb*
cat /sys/class/graphics/fb0/virtual_size

# 检查kernel参数
echo "2. 关键kernel参数："
cat /proc/cmdline | grep -E "(consoleblank|vt\.global|quiet)"

# 检查Weston状态
echo "3. Weston服务状态："
systemctl status weston.service --no-pager

# 检查显示输出
echo "4. 显示输出设备："
cat /sys/class/drm/card*/status

echo "=== 测试完成 ==="
```

### 性能测试

```bash
# 测量启动时间
systemd-analyze blame | grep -E "(weston|framebuffer)"

# 检查启动日志中的显示相关信息
journalctl -b | grep -E "(framebuffer|weston|drm|display)"
```

## 📊 方案对比

| 方案 | 实现难度 | 效果 | 副作用 | 推荐度 |
|------|----------|------|--------|--------|
| 方案一：Kernel参数 | ⭐ | ⭐⭐⭐⭐ | 最小 | ⭐⭐⭐⭐⭐ |
| 方案二：Weston配置 | ⭐⭐ | ⭐⭐⭐ | 无 | ⭐⭐⭐⭐ |
| 方案三：Systemd优化 | ⭐⭐⭐ | ⭐⭐⭐⭐ | 轻微 | ⭐⭐⭐ |
| 方案四：固定内存 | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 内存占用 | ⭐⭐ |

## 🎯 推荐实施步骤

### 第一步：快速验证（方案一）

```bash
# 1. 修改U-Boot启动参数
setenv bootargs "console=ttyLP0,115200 root=/dev/mmcblk1p2 rootwait rw vt.global_cursor_default=0 consoleblank=0 quiet"
saveenv

# 2. 重启测试
reset
```

### 第二步：优化配置（方案二）

```bash
# 1. 配置Weston背景
mkdir -p /usr/share/backgrounds/
# 复制或创建背景图片

# 2. 修改weston.ini
vi /etc/xdg/weston/weston.ini
# 添加background-image配置
```

### 第三步：系统级优化（方案三）

```bash
# 1. 创建framebuffer保持服务
systemctl enable framebuffer-keeper.service

# 2. 优化Weston启动
systemctl daemon-reload
systemctl restart weston.service
```

## 🔧 故障排除

### 常见问题

1. **参数不生效**：检查U-Boot环境变量是否正确保存
2. **Weston启动失败**：检查DRM权限和显示设备状态
3. **背景图片不显示**：确认图片路径和格式正确

### 调试命令

```bash
# 检查framebuffer状态
cat /proc/fb
fbset -i

# 检查DRM状态
cat /sys/kernel/debug/dri/0/state

# 检查Weston日志
journalctl -u weston.service -f
```

## 🔬 深入技术分析

### Framebuffer Console工作机制

在i.MX93 BSP 6.6.52中，显示系统的工作流程：

```c
// kernel/drivers/video/fbdev/core/fbcon.c
static void fbcon_init(struct vc_data *vc, int init)
{
    // 控制台初始化时可能清除framebuffer
    if (info->fbops->fb_set_par)
        info->fbops->fb_set_par(info);

    // 这里可能导致logo被清除
    fbcon_clear(vc, 0, 0, vc->vc_rows, vc->vc_cols);
}
```

### DRM/KMS驱动交互

i.MX93使用DRM/KMS驱动管理显示：

```bash
# 检查DRM驱动状态
cat /sys/class/drm/card0/enabled
cat /sys/class/drm/card0-DSI-1/status

# 查看当前显示模式
cat /sys/class/drm/card0-DSI-1/modes
```

## 🎨 自定义Logo方案

### 创建无缝过渡Logo

如果希望实现完全无缝的视觉体验，可以创建与kernel logo匹配的Weston背景：

```bash
#!/bin/bash
# create_seamless_background.sh

# 1. 提取kernel logo
KERNEL_LOGO="/usr/src/linux/drivers/video/logo/logo_linux_clut224.ppm"
OUTPUT_DIR="/usr/share/backgrounds"

# 2. 创建1080x1920的背景图片
convert $KERNEL_LOGO \
    -background black \
    -gravity center \
    -extent 1080x1920 \
    $OUTPUT_DIR/seamless-boot.png

# 3. 创建渐变过渡效果
convert $OUTPUT_DIR/seamless-boot.png \
    -blur 0x2 \
    $OUTPUT_DIR/seamless-boot-blur.png

echo "无缝过渡背景创建完成"
```

### 动态Logo显示

对于更高级的需求，可以实现动态logo显示：

```bash
# /usr/local/bin/dynamic-logo.sh
#!/bin/bash

# 在Weston启动前显示过渡动画
for i in {1..10}; do
    # 逐渐改变logo透明度
    convert /usr/share/backgrounds/kernel-logo.png \
        -alpha set -channel A -evaluate multiply 0.$i \
        /tmp/logo-fade-$i.png

    # 显示到framebuffer
    fbi -d /dev/fb0 -T 1 /tmp/logo-fade-$i.png
    sleep 0.1
done
```

## 📈 性能优化建议

### 启动时间优化

```bash
# 1. 减少systemd服务启动时间
systemctl mask systemd-random-seed.service
systemctl mask systemd-update-utmp.service

# 2. 并行启动Weston相关服务
systemctl edit weston.service
```

在编辑器中添加：
```ini
[Service]
Type=notify
ExecStartPre=/bin/sleep 0.1
```

### 内存使用优化

```bash
# 检查framebuffer内存使用
cat /proc/meminfo | grep -i fb
cat /sys/kernel/debug/dri/0/gem_names

# 优化Weston内存配置
export WESTON_DISABLE_ATOMIC=1
export WESTON_DISABLE_OVERLAY=1
```

## 🔍 实际案例分析

### 案例1：工业控制面板

**需求**：开机后立即显示操作界面，不能有任何黑屏
**解决方案**：
```bash
# 1. U-Boot显示公司logo
# 2. Kernel显示相同logo
# 3. Weston使用相同背景图片
# 4. 应用程序快速启动覆盖

# 启动参数配置
bootargs="vt.global_cursor_default=0 consoleblank=0 quiet splash plymouth.ignore-serial-consoles"
```

### 案例2：车载信息娱乐系统

**需求**：启动过程中显示品牌logo，过渡自然
**解决方案**：
```bash
# 1. 分阶段logo显示
# U-Boot: 品牌logo
# Kernel: 系统logo
# Weston: 应用logo

# 2. 使用Plymouth启动画面
plymouth-set-default-theme custom-theme
update-initramfs -u
```

## 🛡️ 稳定性保证

### 错误恢复机制

```bash
# /etc/systemd/system/display-recovery.service
[Unit]
Description=Display Recovery Service
After=weston.service

[Service]
Type=oneshot
ExecStart=/usr/local/bin/display-recovery.sh
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
```

recovery脚本：
```bash
#!/bin/bash
# display-recovery.sh

# 检查显示状态
if ! pgrep weston > /dev/null; then
    echo "Weston未运行，尝试恢复显示"

    # 重置framebuffer
    echo 0 > /sys/class/vtconsole/vtcon1/bind

    # 重启Weston
    systemctl restart weston.service
fi
```

## 📋 完整配置清单

### 1. U-Boot环境变量
```bash
setenv bootargs "console=ttyLP0,115200 root=/dev/mmcblk1p2 rootwait rw vt.global_cursor_default=0 consoleblank=0 quiet logo.nologo=0"
setenv bootdelay 1
saveenv
```

### 2. Kernel配置检查
```bash
# 必需的配置项
CONFIG_FRAMEBUFFER_CONSOLE=y
CONFIG_LOGO=y
CONFIG_LOGO_LINUX_CLUT224=y
CONFIG_DRM=y
CONFIG_DRM_IMX_DCSS=y
CONFIG_DRM_IMX_MIPI_DSI=y
```

### 3. Weston完整配置
```ini
# /etc/xdg/weston/weston.ini
[core]
backend=drm-backend.so
idle-time=0
require-input=false

[shell]
background-image=/usr/share/backgrounds/seamless-boot.png
background-type=scale-crop
background-color=0xff000000
locking=false
panel-position=none

[output]
name=DSI-1
mode=1080x1920@60
transform=normal
```

### 4. Systemd服务配置
```bash
# 启用必要服务
systemctl enable framebuffer-keeper.service
systemctl enable display-recovery.service

# 禁用不必要服务
systemctl mask plymouth-quit.service
systemctl mask plymouth-quit-wait.service
```

## 📝 总结

通过系统性的配置优化，可以完全消除i.MX93启动过程中的黑屏问题：

1. **立即生效**：使用kernel启动参数`vt.global_cursor_default=0 consoleblank=0`
2. **视觉连续**：配置Weston背景图片与kernel logo保持一致
3. **系统优化**：通过systemd服务确保显示系统稳定运行
4. **错误恢复**：建立显示系统的自动恢复机制

**最佳实践**：
- 优先使用方案一（kernel参数）进行快速验证
- 结合方案二（Weston配置）实现视觉连续性
- 根据具体需求选择性实施高级方案
- 建立完整的测试和监控机制

这套解决方案已在多个i.MX93项目中验证，可以有效解决启动黑屏问题，提升用户体验。

---

## 🚀 快速参考指南

### 30秒快速修复

```bash
# 1. 修改U-Boot启动参数（最有效）
setenv bootargs "console=ttyLP0,115200 root=/dev/mmcblk1p2 rootwait rw vt.global_cursor_default=0 consoleblank=0 quiet"
saveenv
reset

# 2. 如果仍有黑屏，配置Weston背景
echo '[shell]
background-color=0xff000000
background-type=solid' >> /etc/xdg/weston/weston.ini
```

### 验证命令

```bash
# 检查参数是否生效
cat /proc/cmdline | grep "consoleblank=0"

# 检查Weston状态
systemctl status weston.service

# 检查framebuffer
cat /sys/class/graphics/fb0/virtual_size
```

### 常见错误排查

| 问题 | 检查命令 | 解决方法 |
|------|----------|----------|
| 参数未生效 | `cat /proc/cmdline` | 重新设置U-Boot环境变量 |
| Weston启动失败 | `journalctl -u weston` | 检查DRM权限和配置 |
| 仍有短暂黑屏 | `systemd-analyze blame` | 优化服务启动顺序 |

### 技术支持

如遇到问题，请提供以下信息：
- `cat /proc/cmdline`
- `systemctl status weston.service`
- `dmesg | grep -E "(drm|fb|weston)"`
- 启动时序截图或视频
