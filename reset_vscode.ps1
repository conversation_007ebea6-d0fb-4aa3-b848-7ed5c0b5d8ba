<#
.SYNOPSIS
Reset VS Code machine code by cleaning local settings and cache.

.DESCRIPTION
This script removes VS Code machine-specific identifiers by deleting:
- AppData\Roaming\Code (settings, extensions)
- .vscode folder in user profile (project settings)
Creates a backup before deletion.
#>

# Backup current timestamp for backup folder
$timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
$backupDir = "$env:USERPROFILE\vscode_backup_$timestamp"

# VS Code directories to reset
$targetDirs = @(
    "$env:APPDATA\Code",       # Main settings and extensions
    "$env:USERPROFILE\.vscode" # Project settings
)

Write-Host "VS Code Machine Code Reset Script"
Write-Host "This will remove all VS Code settings and extensions."
Write-Host "A backup will be created at: $backupDir"

# Create backup directory
New-Item -ItemType Directory -Path $backupDir | Out-Null

# Backup and remove target directories
foreach ($dir in $targetDirs) {
    if (Test-Path $dir) {
        Write-Host "Backing up and removing: $dir"
        Copy-Item -Path $dir -Destination "$backupDir\" -Recurse -Force
        Remove-Item -Path $dir -Recurse -Force
    }
}

Write-Host "`nVS Code machine code has been reset."
Write-Host "Original settings backed up to: $backupDir"
Write-Host "Restart VS Code for changes to take effect."