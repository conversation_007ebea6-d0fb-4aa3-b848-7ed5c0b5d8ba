# genIVT.pl 脚本修改指南 - 适配Kernel和DTB签名

## 概述

本文档详细说明如何修改NXP提供的默认`genIVT.pl`脚本，使其能够为kernel和DTB分别生成正确的Image Vector Table (IVT)。

## 默认genIVT.pl脚本分析

### 原始脚本内容
```perl
#! /usr/bin/perl -w
use strict;
open(my $out, '>:raw', 'ivt.bin') or die "Unable to open: $!";
print $out pack("V", 0x412000D1); # Signature
print $out pack("V", 0x80800000); # Load Address (*load_address)
print $out pack("V", 0x0); # Reserved
print $out pack("V", 0x0); # DCD pointer
print $out pack("V", 0x0); # Boot Data
print $out pack("V", 0x80E6A000); # Self Pointer (*ivt)
print $out pack("V", 0x80E6A020); # CSF Pointer (*csf)
print $out pack("V", 0x0); # Reserved
close($out);
```

## 需要修改的关键字段

### 字段1：Load Address（第2行）
```perl
print $out pack("V", 0x80800000); # Load Address (*load_address)
```

**修改说明**：
- **Kernel**: 保持 `0x80800000`
- **DTB**: 改为 `0x83000000`（或您的DTB加载地址）

### 字段2：Self Pointer（第6行）⚠️ **最重要**
```perl
print $out pack("V", 0x80E6A000); # Self Pointer (*ivt)
```

**修改说明**：
- **Kernel**: `0x80800000 + IVT偏移` = `0x80800000 + 0x66A000` = `0x80E6A000`
- **DTB**: `0x83000000 + IVT偏移` = `0x83000000 + 0x100000` = `0x83100000`

### 字段3：CSF Pointer（第7行）⚠️ **最重要**
```perl
print $out pack("V", 0x80E6A020); # CSF Pointer (*csf)
```

**修改说明**：
- **Kernel**: `Self Pointer + 0x20` = `0x80E6A000 + 0x20` = `0x80E6A020`
- **DTB**: `Self Pointer + 0x20` = `0x83100000 + 0x20` = `0x83100020`

## 修改后的脚本版本

### 版本1：Kernel专用
```perl
#! /usr/bin/perl -w
use strict;
open(my $out, '>:raw', 'ivt_kernel.bin') or die "Unable to open: $!";
print $out pack("V", 0x412000D1); # Signature
print $out pack("V", 0x80800000); # Load Address (kernel)
print $out pack("V", 0x0); # Reserved
print $out pack("V", 0x0); # DCD pointer
print $out pack("V", 0x0); # Boot Data
print $out pack("V", 0x80E6A000); # Self Pointer (0x80800000 + 0x66A000)
print $out pack("V", 0x80E6A020); # CSF Pointer (Self + 0x20)
print $out pack("V", 0x0); # Reserved
close($out);
```

### 版本2：DTB专用
```perl
#! /usr/bin/perl -w
use strict;
open(my $out, '>:raw', 'ivt_dtb.bin') or die "Unable to open: $!";
print $out pack("V", 0x412000D1); # Signature
print $out pack("V", 0x83000000); # Load Address (DTB)
print $out pack("V", 0x0); # Reserved
print $out pack("V", 0x0); # DCD pointer
print $out pack("V", 0x0); # Boot Data
print $out pack("V", 0x83100000); # Self Pointer (0x83000000 + 0x100000)
print $out pack("V", 0x83100020); # CSF Pointer (Self + 0x20)
print $out pack("V", 0x0); # Reserved
close($out);
```

### 版本3：通用参数化版本（推荐）
```perl
#! /usr/bin/perl -w
use strict;

# 检查参数
if (@ARGV != 3) {
    print "Usage: $0 <load_address> <ivt_offset> <output_file>\n";
    print "Example for kernel: $0 0x80800000 0x66A000 ivt_kernel.bin\n";
    print "Example for DTB: $0 0x83000000 0x100000 ivt_dtb.bin\n";
    exit 1;
}

my ($load_addr_str, $ivt_offset_str, $output_file) = @ARGV;
my $load_addr = hex($load_addr_str);
my $ivt_offset = hex($ivt_offset_str);

# 计算地址
my $self_addr = $load_addr + $ivt_offset;
my $csf_addr = $self_addr + 0x20;

print sprintf("Load Address: 0x%08X\n", $load_addr);
print sprintf("Self Address: 0x%08X\n", $self_addr);
print sprintf("CSF Address:  0x%08X\n", $csf_addr);

open(my $out, '>:raw', $output_file) or die "Unable to open: $!";
print $out pack("V", 0x412000D1); # Signature
print $out pack("V", $load_addr);  # Load Address - 修改点1
print $out pack("V", 0x0); # Reserved
print $out pack("V", 0x0); # DCD pointer
print $out pack("V", 0x0); # Boot Data
print $out pack("V", $self_addr); # Self Pointer - 修改点2
print $out pack("V", $csf_addr);  # CSF Pointer - 修改点3
print $out pack("V", 0x0); # Reserved
close($out);

print "IVT generated: $output_file\n";
```

## 使用方法

### 使用通用版本
```bash
# 为kernel生成IVT
perl genIVT.pl 0x80800000 0x66A000 ivt_kernel.bin

# 为DTB生成IVT
perl genIVT.pl 0x83000000 0x100000 ivt_dtb.bin
```

### 验证生成的IVT
```bash
# 查看生成的IVT内容
hexdump -C ivt_kernel.bin
hexdump -C ivt_dtb.bin

# 应该看到：
# Kernel IVT: D1 00 20 41 00 00 80 80 ... 00 A0 E6 80 20 A0 E6 80
# DTB IVT:    D1 00 20 41 00 00 00 83 ... 00 00 10 83 20 00 10 83
```

## 总结

**最关键的3个修改点**：

1. **Load Address**：kernel用`0x80800000`，DTB用`0x83000000`
2. **Self Pointer**：`Load Address + IVT偏移`
3. **CSF Pointer**：`Self Pointer + 0x20`

其他字段（Signature、Reserved、DCD pointer、Boot Data）对于kernel和DTB都保持不变。

## 注意事项

- IVT偏移应该是镜像4KB对齐后的大小
- Self Pointer指向IVT在内存中的位置
- CSF Pointer指向CSF数据的起始位置（IVT后32字节）
- 确保加载地址与U-Boot中的实际加载地址一致
