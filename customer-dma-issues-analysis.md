# 客户ECSPI DMA问题深度分析

## 问题总结

### 客户反馈的问题
1. **双重SMC设置**: `spi_imx_configure_burst_length`和`spi_imx->devtype_data->trigger`都设置SMC
2. **数据传输错误**: 字节间隔消除了，但数据内容不正确
3. **波形异常**: 多数字节被当成4字节发送

### 测试场景
- **transfer->len**: 2048字节
- **burst_len**: 64 * 4 = 256字节 
- **DMA阈值**: TX_THRESHOLD=16, RX_THRESHOLD=16
- **预期数据**: 0x7e, 0x7e, 0x3, 0x00, 0xa1, 0x8, 0xe4, 0x95...

## 问题1: 双重SMC设置分析

### 代码流程分析
```c
// 在spi_imx_dma_transfer中:
1. spi_imx_configure_burst_length(spi_imx, transfer);  // 第一次设置SMC
2. spi_imx->devtype_data->trigger(spi_imx);           // 第二次设置SMC
```

### spi_imx_configure_burst_length函数
```c
static void spi_imx_configure_burst_length(...)
{
    // ... burst length配置 ...
    ctrl |= MX51_ECSPI_CTRL_SMC;  // 第一次设置SMC=1
    writel(ctrl, spi_imx->base + MXC_CSPICTRL);
}
```

### mx51_ecspi_trigger函数
```c
static void mx51_ecspi_trigger(struct spi_imx_data *spi_imx)
{
    u32 reg = readl(spi_imx->base + MX51_ECSPI_CTRL);
    
    if (!spi_imx->usedma)
        reg |= MX51_ECSPI_CTRL_XCH;
    else if (spi_imx->devtype_data->devtype == IMX6UL_ECSPI)
        reg |= MX51_ECSPI_CTRL_SMC;  // 第二次设置SMC=1 (i.MX6ULL)
    else
        reg &= ~MX51_ECSPI_CTRL_SMC;
        
    writel(reg, spi_imx->base + MX51_ECSPI_CTRL);
}
```

### 问题分析
**双重SMC设置本身不是问题**，因为：
1. 两次都是设置SMC=1，结果相同
2. i.MX6ULL确实需要SMC=1模式
3. 第二次设置是冗余的，但不会造成功能错误

## 问题2: 数据传输错误的根本原因

### 可能的原因分析

#### 原因1: BURST_LENGTH计算错误
```c
// 当前逻辑 (可能有问题):
if (transfer->len >= 1024) {
    burst_length_bits = 8192;  // 1024 bytes = 8192 bits
}

// 问题: 对于2048字节传输，设置1024字节burst
// 但硬件限制是4096 bits = 512字节
burst_length_bits = min(burst_length_bits, 4096U);  // 实际变成512字节
```

#### 原因2: WML (Watermark Level) 配置冲突
```c
// 在spi_imx_dma_transfer开始:
bytes_per_word = spi_imx_bytes_per_word(transfer->bits_per_word);
for (i = spi_imx->devtype_data->fifo_size / 2; i > 0; i--) {
    if (!(sg_dma_len(last_sg) % (i * bytes_per_word)))
        break;
}
spi_imx->wml = i;  // 这里计算的WML可能与burst length不匹配
```

#### 原因3: DMA配置与ECSPI配置不一致
```c
// DMA配置:
tx.dst_maxburst = min(spi_imx->wml, 8U);  // 最大8个字
rx.src_maxburst = min(spi_imx->wml, 8U);  // 最大8个字

// ECSPI配置:
burst_length_bits = 4096;  // 512字节 = 128个32位字

// 不匹配: DMA每次传输8个字，但ECSPI期望128个字的burst
```

## 问题3: DMA/FIFO交互机制详解

### 客户的疑问回答

#### 疑问1: "接收8个字节的时候不去检测，只有达到域值才去检测？"
**回答**: 不是的。ECSPI的检测机制是：
```
SMC=1模式下的检测逻辑:
1. 持续监控TXFIFO状态
2. 只要FIFO非空(>0字节)就开始传输
3. TX_THRESHOLD控制DMA填充时机，不是传输开始条件
```

#### 疑问2: "transfer->len > burst len时，burst len完成接下来如何处理？"
**回答**: 
```
大传输的处理流程:
1. 第一个burst: 传输512字节 (4096 bits)
2. CS保持有效，无间隔
3. 第二个burst: 继续传输512字节
4. 重复直到完成2048字节
5. 最后CS释放

关键: 多个burst之间应该无间隔，CS保持有效
```

## 问题4: "多数字节当成4字节发送"的分析

### 可能的原因

#### 原因1: bits_per_word配置错误
```c
// 检查SPI配置:
transfer->bits_per_word = ?  // 应该是8，如果是32就会有问题

// 如果bits_per_word=32:
bytes_per_word = 4;  // 每个"字"是4字节
// 这会导致数据解释错误
```

#### 原因2: DMA数据宽度配置错误
```c
// 在spi_imx_dma_configure中:
buswidth = spi_imx_bytes_per_word(transfer->bits_per_word);

tx.dst_addr_width = buswidth;  // 如果buswidth=4，每次传输4字节
rx.src_addr_width = buswidth;

// 如果buswidth=4但实际应该是1，就会出现"4字节发送"现象
```

#### 原因3: FIFO数据打包
```c
// ECSPI FIFO是32位宽度
// 如果配置不当，可能会将4个8位数据打包成1个32位数据发送
```

## 修复方案

### 方案1: 修正BURST_LENGTH与DMA配置的匹配
```c
static void spi_imx_configure_burst_length(struct spi_imx_data *spi_imx,
                                           struct spi_transfer *transfer)
{
    u32 ctrl;
    unsigned int burst_length_bits;
    unsigned int bytes_per_word = spi_imx_bytes_per_word(transfer->bits_per_word);
    
    // 确保burst length与WML匹配
    unsigned int max_burst_bytes = spi_imx->wml * bytes_per_word;
    unsigned int desired_burst_bytes;
    
    if (transfer->len >= 512) {
        desired_burst_bytes = 512;
    } else if (transfer->len >= 256) {
        desired_burst_bytes = 256;
    } else {
        desired_burst_bytes = transfer->len;
    }
    
    // 确保burst length是WML的倍数
    desired_burst_bytes = min(desired_burst_bytes, max_burst_bytes);
    burst_length_bits = desired_burst_bytes * 8;
    
    // 硬件限制
    burst_length_bits = min(burst_length_bits, 4096U);
    
    ctrl = readl(spi_imx->base + MXC_CSPICTRL);
    ctrl &= ~MXC_CSPICTRL_BL_MASK;
    ctrl |= (burst_length_bits - 1) << MXC_CSPICTRL_BL_OFFSET;
    ctrl |= MX51_ECSPI_CTRL_SMC;
    
    writel(ctrl, spi_imx->base + MXC_CSPICTRL);
}
```

### 方案2: 移除冗余的SMC设置
```c
// 修改mx51_ecspi_trigger函数，避免重复设置SMC
static void mx51_ecspi_trigger(struct spi_imx_data *spi_imx)
{
    u32 reg = readl(spi_imx->base + MX51_ECSPI_CTRL);
    
    if (!spi_imx->usedma) {
        reg |= MX51_ECSPI_CTRL_XCH;
    } else {
        // 对于DMA模式，SMC已经在spi_imx_configure_burst_length中设置
        // 这里不需要重复设置
        if (spi_imx->devtype_data->devtype != IMX6UL_ECSPI) {
            reg &= ~MX51_ECSPI_CTRL_SMC;
        }
        // i.MX6ULL的SMC已经设置，不需要重复
    }
    
    writel(reg, spi_imx->base + MX51_ECSPI_CTRL);
}
```

### 方案3: 确保数据宽度配置正确
```c
// 在应用层确保:
transfer->bits_per_word = 8;  // 强制8位模式

// 在驱动中验证:
if (transfer->bits_per_word != 8) {
    dev_warn(spi_imx->dev, "Only 8-bit transfers supported in DMA mode\n");
    transfer->bits_per_word = 8;
}
```

## 调试建议

### 1. 添加调试信息
```c
// 在spi_imx_configure_burst_length中添加:
dev_info(spi_imx->dev, "Transfer: len=%d, bits_per_word=%d, burst_bits=%d\n",
         transfer->len, transfer->bits_per_word, burst_length_bits);

// 在spi_imx_dma_configure中添加:
dev_info(spi_imx->dev, "DMA: wml=%d, bytes_per_word=%d, maxburst=%d\n",
         spi_imx->wml, bytes_per_word, min(spi_imx->wml, 8U));
```

### 2. 寄存器验证
```bash
# 传输前检查寄存器:
./memtool ECSPI2.CONREG  # 检查BURST_LENGTH和SMC
./memtool ECSPI2.DMAREG  # 检查DMA阈值
```

### 3. 数据对比
```c
// 在传输前后打印数据:
print_hex_dump(KERN_INFO, "TX: ", DUMP_PREFIX_OFFSET, 16, 1,
               transfer->tx_buf, min(transfer->len, 64), true);
```

## 总结

主要问题可能是：
1. **BURST_LENGTH与WML不匹配**导致数据传输异常
2. **bits_per_word配置错误**导致4字节打包发送
3. **DMA配置与ECSPI配置不一致**

建议按照修复方案逐步调试，重点关注数据宽度配置和burst length计算。
