# NXP LSDK 21.08 Kernel 5.10.35 NAPI Budget修改指南

## LSDK 21.08代码结构

LSDK 21.08使用的是定制化的Linux kernel 5.10.35，代码结构与标准内核略有不同。

### 源码获取
```bash
# 获取LSDK 21.08源码
git clone https://github.com/nxp/linux.git -b LSDK-21.08
cd linux
```

## 1. DPAA2以太网驱动修改

### 1.1 主要文件位置
- `drivers/net/ethernet/freescale/dpaa2/dpaa2-eth.c`
- `drivers/net/ethernet/freescale/dpaa2/dpaa2-eth.h`

### 1.2 修改dpaa2-eth.h头文件

**文件**: `drivers/net/ethernet/freescale/dpaa2/dpaa2-eth.h`

```c
// 在文件顶部添加，大约在第50行左右
#ifndef DPAA2_ETH_NAPI_BUDGET
#define DPAA2_ETH_NAPI_BUDGET 128  /* 从64增加到128 */
#endif

// 找到现有的宏定义，通常在第100行左右
#define DPAA2_ETH_STORE_SIZE		16
// 在这附近添加或修改：
#define DPAA2_ETH_NAPI_WEIGHT		DPAA2_ETH_NAPI_BUDGET

// 如果存在以下定义，需要修改：
// #define DPAA2_ETH_NAPI_WEIGHT	NAPI_POLL_WEIGHT
// 改为：
// #define DPAA2_ETH_NAPI_WEIGHT	DPAA2_ETH_NAPI_BUDGET
```

### 1.3 修改dpaa2-eth.c主驱动文件

**文件**: `drivers/net/ethernet/freescale/dpaa2/dpaa2-eth.c`

#### 1.3.1 添加模块参数（在文件顶部，大约第50行）

```c
// 在现有的模块参数附近添加
static bool tx_pause_frames = true;
module_param(tx_pause_frames, bool, 0444);
MODULE_PARM_DESC(tx_pause_frames,
		 "Enable Tx pause frame generation");

// 添加新的模块参数
static int napi_budget = DPAA2_ETH_NAPI_BUDGET;
module_param(napi_budget, int, 0644);
MODULE_PARM_DESC(napi_budget, "NAPI budget for DPAA2 ethernet (default: 128)");

static int netdev_budget_multiplier = 2;
module_param(netdev_budget_multiplier, int, 0644);
MODULE_PARM_DESC(netdev_budget_multiplier, "Multiplier for netdev_budget (default: 2)");
```

#### 1.3.2 修改NAPI轮询函数（大约第1500行左右）

找到 `dpaa2_eth_poll` 函数：

```c
// 原始函数签名
static int dpaa2_eth_poll(struct napi_struct *napi, int budget)
{
	struct dpaa2_eth_channel *ch;
	struct dpaa2_eth_priv *priv;
	int rx_cleaned = 0, txconf_cleaned = 0;
	struct dpaa2_eth_fq *fq, *next;
	int store_cleaned, work_done;
	struct list_head rx_list;
	int err;

	ch = container_of(napi, struct dpaa2_eth_channel, napi);
	ch->stats.frames = 0;

	INIT_LIST_HEAD(&rx_list);

	// 修改这里：使用自定义budget
	int effective_budget = min(budget, napi_budget);
	
	do {
		err = dpaa2_io_service_pull_channel(ch->dpio, ch->ch_id,
						    ch->store);
		if (unlikely(err))
			break;

		/* Refill pool if appropriate */
		refill_pool(priv, ch, priv->bpool);

		store_cleaned = consume_frames(ch, &rx_list);
		if (store_cleaned <= 0)
			break;
		if (store_cleaned > 0) {
			rx_cleaned += store_cleaned;
			/* More work available */
			if (rx_cleaned >= effective_budget)  // 使用effective_budget
				break;
		}
	} while (store_cleaned > 0);

	/* Update NET DIM with the values for this CDAN */
	dpaa2_io_update_net_dim(ch->dpio, ch->stats.frames, rx_cleaned);
	ch->stats.frames = 0;

	/* We didn't consume the entire budget, so finish napi and
	 * re-enable data availability notifications
	 */
	if (rx_cleaned < effective_budget &&  // 使用effective_budget
	    napi_complete_done(napi, rx_cleaned)) {
		/* Re-enable data available notifications */
		do {
			err = dpaa2_io_service_rearm(ch->dpio, &ch->nctx);
			cpu_relax();
		} while (err == -EBUSY);
		WARN_ONCE(err, "CDAN notifications rearm failed on core %d",
			  ch->nctx.desired_cpu);

		ch->stats.cdan++;
	}

	txconf_cleaned = dpaa2_eth_txconf(ch, effective_budget);  // 使用effective_budget

	work_done = max(rx_cleaned, 1);

	return work_done;
}
```

#### 1.3.3 修改NAPI初始化函数（大约第4000行左右）

找到 `add_ch_napi` 函数：

```c
static void add_ch_napi(struct dpaa2_eth_priv *priv)
{
	struct dpaa2_eth_channel *ch;
	int i;

	for (i = 0; i < priv->num_channels; i++) {
		ch = priv->channel[i];
		/* NAPI weight *MUST* be a multiple of DPAA2_ETH_STORE_SIZE */
		// 修改这里：使用自定义的napi_budget
		netif_napi_add(priv->net_dev, &ch->napi, dpaa2_eth_poll,
			       ALIGN(napi_budget, DPAA2_ETH_STORE_SIZE));
	}
}
```

## 2. 修改网络核心参数

### 2.1 修改net/core/dev.c

**文件**: `net/core/dev.c`

#### 2.1.1 修改默认参数（大约第4200行左右）

```c
// 找到现有的定义
int weight_p __read_mostly = 64;
EXPORT_SYMBOL(weight_p);

// 修改为：
int weight_p __read_mostly = 128;  // 从64改为128
EXPORT_SYMBOL(weight_p);

// 找到netdev_budget定义（大约第4210行）
int netdev_budget __read_mostly = 300;
EXPORT_SYMBOL(netdev_budget);

// 修改为：
int netdev_budget __read_mostly = 600;  // 从300改为600
EXPORT_SYMBOL(netdev_budget);
```

#### 2.1.2 修改net_dev_init函数（大约第11000行左右）

```c
static int __init net_dev_init(void)
{
	// 在函数中添加动态调整逻辑
	if (netdev_budget_multiplier > 1) {
		netdev_budget *= netdev_budget_multiplier;
		pr_info("DPAA2: netdev_budget adjusted to %d\n", netdev_budget);
	}
	
	// 其余初始化代码保持不变...
}
```

## 3. LSDK特定的DPAA2优化

### 3.1 修改QBMan驱动参数

**文件**: `drivers/soc/fsl/dpio/dpio-service.c`

```c
// 在文件顶部添加参数
static int qbman_poll_quota = 32;  // 从16增加到32
module_param(qbman_poll_quota, int, 0644);
MODULE_PARM_DESC(qbman_poll_quota, "QBMan polling quota per iteration");

// 找到dpaa2_io_service_pull_channel函数，修改轮询逻辑
int dpaa2_io_service_pull_channel(struct dpaa2_io *d, u32 channelid,
				  struct dpaa2_io_store *s)
{
	// 使用调整后的quota
	// 原有逻辑中如果有固定的轮询次数，改为使用qbman_poll_quota
}
```

### 3.2 修改DPAA2 Buffer Pool管理

**文件**: `drivers/net/ethernet/freescale/dpaa2/dpaa2-eth.c`

在buffer pool相关函数中：

```c
// 找到refill_pool函数，大约第800行
static int refill_pool(struct dpaa2_eth_priv *priv,
		       struct dpaa2_eth_channel *ch,
		       struct dpaa2_eth_bp *bp)
{
	int new_count;
	int err = 0;

	// 增加每次refill的数量
	int refill_batch = DPAA2_ETH_BUFS_PER_CMD * 2;  // 原来可能是*1
	
	if (unlikely(ch->buf_count < DPAA2_ETH_REFILL_THRESH)) {
		do {
			new_count = add_bufs(priv, ch, bp, refill_batch);
			if (unlikely(!new_count)) {
				/* Out of memory; abort for now, we'll try
				 * later on
				 */
				break;
			}
			ch->buf_count += new_count;
		} while (ch->buf_count < DPAA2_ETH_NUM_BUFS);

		if (unlikely(ch->buf_count < DPAA2_ETH_NUM_BUFS))
			err = -ENOMEM;
	}

	return err;
}
```

## 4. 编译配置

### 4.1 修改defconfig

**文件**: `arch/arm64/configs/lsdk.defconfig`

```bash
# 确保启用相关配置
CONFIG_FSL_DPAA2_ETH=y
CONFIG_FSL_DPAA2_ETH_DCB=y
CONFIG_FSL_MC_DPIO=y
CONFIG_FSL_MC_BUS=y

# 添加调试选项（可选）
CONFIG_FSL_DPAA2_ETH_DEBUG=y
```

### 4.2 编译步骤

```bash
# 1. 清理之前的编译
make distclean

# 2. 使用LSDK配置
make lsdk_defconfig

# 3. 可选：手动配置
make menuconfig

# 4. 编译
make -j$(nproc) Image modules dtbs

# 5. 安装模块
make INSTALL_MOD_PATH=/tmp/modules modules_install

# 6. 创建部署包
tar -czf lsdk-21.08-napi-optimized.tar.gz -C /tmp/modules .
```

## 5. 部署和测试

### 5.1 部署脚本

```bash
#!/bin/bash
# deploy_optimized_kernel.sh

KERNEL_IMAGE="arch/arm64/boot/Image"
DTB_PATH="arch/arm64/boot/dts/freescale"
MODULES_PATH="/tmp/modules"

echo "=== 部署LSDK 21.08 NAPI优化内核 ==="

# 备份原始文件
cp /boot/Image /boot/Image.backup
cp -r /lib/modules/$(uname -r) /lib/modules/$(uname -r).backup

# 部署新内核
cp $KERNEL_IMAGE /boot/Image.napi-optimized
cp $DTB_PATH/fsl-lx2160a-rdb.dtb /boot/

# 部署新模块
tar -xzf lsdk-21.08-napi-optimized.tar.gz -C /

# 更新启动配置
echo "请手动更新U-Boot环境变量指向新内核"
```

### 5.2 验证脚本

```bash
#!/bin/bash
# verify_napi_optimization.sh

echo "=== LSDK 21.08 NAPI优化验证 ==="

# 检查模块参数
echo "DPAA2驱动参数:"
cat /sys/module/dpaa2_eth/parameters/napi_budget 2>/dev/null || echo "参数不存在"

# 检查系统参数
echo "系统NAPI参数:"
echo "dev_weight: $(cat /proc/sys/net/core/dev_weight)"
echo "netdev_budget: $(cat /proc/sys/net/core/netdev_budget)"

# 检查DPAA2状态
echo "DPAA2对象状态:"
restool dpni list 2>/dev/null || echo "restool不可用"

# 监控网络统计
echo "网络接口统计:"
ethtool -S eth3 | grep -E "(rx_nobuffer|rx_packets|tx_packets)"

echo "=== 开始性能监控 ==="
watch -n 5 'ethtool -S eth3 | grep rx_nobuffer'
```

## 6. 性能调优参数

### 6.1 运行时调整

```bash
# 应用优化参数
echo 128 > /proc/sys/net/core/dev_weight
echo 600 > /proc/sys/net/core/netdev_budget
echo 5000 > /proc/sys/net/core/netdev_max_backlog

# 重新加载DPAA2驱动
rmmod dpaa2-eth
modprobe dpaa2-eth napi_budget=128

# 验证效果
ethtool -S eth3 | grep rx_nobuffer
```

### 6.2 永久配置

```bash
# 添加到/etc/sysctl.conf
echo 'net.core.dev_weight = 128' >> /etc/sysctl.conf
echo 'net.core.netdev_budget = 600' >> /etc/sysctl.conf

# 添加到/etc/modprobe.d/dpaa2-eth.conf
echo 'options dpaa2-eth napi_budget=128' > /etc/modprobe.d/dpaa2-eth.conf
```

## 7. 预期效果

通过这些修改，您应该看到：

1. **MSI中断频率降低50%**
2. **rx_nobuffer丢包显著减少**
3. **网络吞吐量提升10-20%**
4. **CPU软中断负载降低**

这些修改专门针对LSDK 21.08的代码结构，确保与NXP的DPAA2架构完全兼容。
