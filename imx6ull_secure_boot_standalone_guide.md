# 手把手教你以 Standalone 方式编译i.MX6ULL的Secure Boot

## 📖 前言

本文档基于实际验证的方法，详细介绍如何在i.MX6ULL处理器上实现HABv4（High Assurance Boot version 4）安全启动。采用kernel和DTB统一签名的方式，确保启动镜像的完整性和真实性。

## 🎯 概述

### HAB安全启动原理

i.MX6ULL使用HABv4实现安全启动，通过数字签名验证确保启动镜像的完整性和真实性。

### 安全启动流程图（适配微信阅读，增加分叉）

```mermaid
flowchart LR
    A[Boot ROM启动] --> B{HAB Fuse状态}
    B -->|Open模式| C[开发模式启动]
    B -->|Closed模式| D[安全模式启动]
    C --> E[验证SRK Hash]
    D --> E
    E --> F{SRK验证}
    F -->|成功| G[加载U-Boot]
    F -->|失败| H[启动失败]
    G --> I{U-Boot签名验证}
    I -->|成功| J[加载Kernel+DTB]
    I -->|失败| K[启动失败]
    J --> L{Kernel+DTB签名验证}
    L -->|成功| M[启动Linux系统]
    L -->|失败| N[启动失败]
```

### 镜像签名流程图（基于实践验证）

```mermaid
flowchart LR
    A[zImage原始文件] --> B[objcopy 4KB对齐]
    B --> C[zImage_pad.bin]
    C --> D[genIVT.pl生成IVT]
    D --> E[cat组合zImage_pad_ivt.bin]

    F[DTB原始文件] --> G[保持独立]

    H[修改csf_additional_images.txt] --> I[配置Blocks验证两个文件]
    E --> I
    G --> I
    I --> J[CST工具生成csf_zImage.bin]
    J --> K[cat生成zImage_signed.bin]
    E --> K

    L[证书文件] --> M[SRK表]
    L --> N[CSF证书]
    L --> O[IMG证书]
    M --> I
    N --> I
    O --> I
```

## 2. 环境准备

### 2.1 工具下载

1. **CST工具**（Code Signing Tool）
   - 从NXP官网下载最新版本的CST工具
   - 解压到工作目录：`/opt/cst-3.3.1/`

2. **交叉编译工具链**
   ```bash
   # 安装ARM交叉编译工具链
   sudo apt-get install gcc-arm-linux-gnueabihf
   ```

3. **必要的软件包**
   ```bash
   sudo apt-get install openssl libssl-dev build-essential
   ```

### 2.2 目录结构

```
secure_boot_workspace/
├── cst-3.3.1/                 # CST工具目录
├── keys/                      # 证书和密钥目录
├── images/                    # 镜像文件目录
├── scripts/                   # 脚本目录
└── output/                    # 输出目录
```

## 3. 证书和密钥生成

### 3.1 生成PKI证书树

```bash
cd cst-3.3.1/keys/
./hab4_pki_tree.sh

# 生成的证书文件：
# - SRK_1_2_3_4_table.bin        # SRK表
# - SRK_1_2_3_4_fuse.bin         # SRK熔丝数据
# - CSF1_1_sha256_4096_65537_v3_usr_crt.pem  # CSF证书
# - IMG1_1_sha256_4096_65537_v3_usr_crt.pem  # IMG证书
# - CSF1_1_sha256_4096_65537_v3_usr_key.pem  # CSF私钥
# - IMG1_1_sha256_4096_65537_v3_usr_key.pem  # IMG私钥
```

### 3.2 证书验证

```bash
# 验证证书链
openssl verify -CAfile ../crts/SRK1_sha256_4096_65537_v3_ca_crt.pem \
    CSF1_1_sha256_4096_65537_v3_usr_crt.pem

openssl verify -CAfile CSF1_1_sha256_4096_65537_v3_usr_crt.pem \
    IMG1_1_sha256_4096_65537_v3_usr_crt.pem
```

## 4. U-Boot配置和编译

### 4.1 U-Boot配置

```bash
# 配置U-Boot支持HAB
make mx6ull_14x14_evk_defconfig
make menuconfig

# 在menuconfig中启用：
# ARM architecture -> Support i.MX HAB features
# ARM architecture -> Support SDP image mode
```

### 4.2 编译U-Boot

```bash
export ARCH=arm
export CROSS_COMPILE=arm-linux-gnueabihf-
make -j$(nproc)

# 生成文件：
# - u-boot.bin
# - u-boot.imx
```

### 4.3 U-Boot签名

#### 4.3.1 创建U-Boot CSF文件

```bash
cat > csf_uboot.txt << 'EOF'
[Header]
    Version = 4.2
    Hash Algorithm = sha256
    Engine Configuration = 0
    Certificate Format = X509
    Signature Format = CMS
    Engine = CAAM

[Install SRK]
    File = "SRK_1_2_3_4_table.bin"
    Source index = 0

[Install CSFK]
    File = "CSF1_1_sha256_4096_65537_v3_usr_crt.pem"

[Authenticate CSF]

[Install Key]
    Verification index = 0
    Target Index = 2
    File = "IMG1_1_sha256_4096_65537_v3_usr_crt.pem"

[Authenticate Data]
    Verification index = 2
    Blocks = 0x87800000 0x0 0x6F000 "u-boot.imx"
EOF
```

#### 4.3.2 生成U-Boot签名

```bash
# 生成CSF二进制文件
../linux64/bin/cst -i csf_uboot.txt -o csf_uboot.bin

# 创建签名的U-Boot
cat u-boot.imx csf_uboot.bin > u-boot-signed.imx
```

## 5. Kernel和DTB签名（基于实践验证） ⭐

### 5.1 准备镜像文件

```bash
# 复制kernel和DTB到工作目录
cp /path/to/zImage ./
cp /path/to/imx6ull-14x14-evk.dtb ./

# 计算kernel镜像大小和4KB对齐
KERNEL_SIZE=$(stat -c%s zImage)
KERNEL_ALIGNED=$(((KERNEL_SIZE + 4095) & ~4095))

echo "Kernel size: $KERNEL_SIZE"
echo "Kernel aligned: $KERNEL_ALIGNED (0x$(printf '%X' $KERNEL_ALIGNED))"
```

### 5.2 Kernel镜像对齐和IVT生成

```bash
# 使用objcopy进行4KB对齐填充
objcopy -I binary -O binary --pad-to $(printf "0x%X" $KERNEL_ALIGNED) --gap-fill=0x00 zImage zImage_pad.bin

# 验证填充结果
ls -la zImage_pad.bin
echo "填充后大小: $(stat -c%s zImage_pad.bin)"
```

### 5.3 生成IVT（Image Vector Table）

```bash
# 修改genIVT.pl脚本中的关键参数
# Load Address: 0x80800000 (kernel加载地址)
# Self Pointer: 0x80800000 + KERNEL_ALIGNED (IVT位置)
# CSF Pointer: Self Pointer + 0x20 (CSF位置)

# 执行genIVT.pl生成ivt.bin
perl genIVT.pl

# 将IVT附加到填充后的zImage末尾
cat zImage_pad.bin ivt.bin > zImage_pad_ivt.bin

echo "zImage_pad_ivt.bin生成完成"
ls -la zImage_pad_ivt.bin
```

### 5.4 修改genIVT.pl脚本

根据实际验证的参数修改genIVT.pl脚本：

```perl
#! /usr/bin/perl -w
use strict;

# 根据实际项目验证的参数
my $load_addr = 0x80800000;        # kernel加载地址
my $ivt_offset = 0x66A000;         # IVT偏移（根据实际kernel大小调整）
my $self_addr = $load_addr + $ivt_offset;  # Self Pointer
my $csf_addr = $self_addr + 0x20;          # CSF Pointer

open(my $out, '>:raw', 'ivt.bin') or die "Unable to open: $!";
print $out pack("V", 0x412000D1); # Signature
print $out pack("V", $load_addr);  # Load Address
print $out pack("V", 0x0); # Reserved
print $out pack("V", 0x0); # DCD pointer
print $out pack("V", 0x0); # Boot Data
print $out pack("V", $self_addr); # Self Pointer (0x80E6A000)
print $out pack("V", $csf_addr);  # CSF Pointer (0x80E6A020)
print $out pack("V", 0x0); # Reserved
close($out);

print "IVT generated with:\n";
print sprintf("Load Address: 0x%08X\n", $load_addr);
print sprintf("Self Address: 0x%08X\n", $self_addr);
print sprintf("CSF Address:  0x%08X\n", $csf_addr);
```

### 5.5 创建CSF文件（关键步骤） ⭐

基于csf_additional_images.txt模板，修改配置以同时签名kernel和DTB：

```bash
# 复制CSF模板文件
cp doc/imx/habv4/csf_examples/additional_images/csf_additional_images.txt csf_zImage.txt

# 修改CSF文件内容
cat > csf_zImage.txt << EOF
[Header]
    Version = 4.2
    Hash Algorithm = sha256
    Engine Configuration = 0
    Certificate Format = X509
    Signature Format = CMS
    Engine = CAAM

[Install SRK]
    File = "SRK_1_2_3_4_table.bin"
    Source index = 0

[Install CSFK]
    File = "CSF1_1_sha256_4096_65537_v3_usr_crt.pem"

[Authenticate CSF]

[Install Key]
    Verification index = 0
    Target Index = 2
    File = "IMG1_1_sha256_4096_65537_v3_usr_crt.pem"

[Authenticate Data]
    Verification index = 2
    # 关键：同时验证kernel和DTB，但保持为两个独立文件
    Blocks = 0x80800000 0x00000000 0x0066A020 "zImage_pad_ivt.bin", \\
             0x83000000 0x00000000 0x0000B927 "imx6ull-14x14-evk.dtb"
EOF

echo "CSF文件创建完成"
```

#### 5.5.1 CSF配置说明（重要）

```bash
# 关键理解：
# 1. kernel和DTB仍然是两个独立的文件
# 2. 通过CSF的Blocks配置，一次性对两个文件进行签名验证
# 3. 生成的csf_zImage.bin包含了对zImage_pad_ivt.bin和DTB文件的签名信息
# 4. 最终只有一个签名文件：zImage_signed.bin

# Blocks参数详解：
# 第一行：0x80800000 0x00000000 0x0066A020 "zImage_pad_ivt.bin"
#   - 0x80800000: kernel在DDR中的加载地址
#   - 0x00000000: 在zImage_pad_ivt.bin文件中的偏移
#   - 0x0066A020: kernel+IVT的大小（包含32字节IVT）
#
# 第二行：0x83000000 0x00000000 0x0000B927 "imx6ull-14x14-evk.dtb"
#   - 0x83000000: DTB在DDR中的加载地址
#   - 0x00000000: 在DTB文件中的偏移
#   - 0x0000B927: DTB文件的实际大小
```

### 5.6 生成签名文件

```bash
# 使用CST工具生成签名（关键步骤）
./cst -i csf_zImage.txt -o csf_zImage.bin

echo "CSF签名文件生成完成"
ls -la csf_zImage.bin
```

### 5.7 创建最终签名镜像

```bash
# 将CSF二进制文件附加到zImage_pad_ivt.bin的末尾
cat zImage_pad_ivt.bin csf_zImage.bin > zImage_signed.bin

echo "=== 最终签名镜像生成完成 ==="
ls -la zImage_signed.bin

# 验证签名镜像大小
SIGNED_SIZE=$(stat -c%s zImage_signed.bin)
echo "签名镜像大小: $SIGNED_SIZE bytes (0x$(printf '%X' $SIGNED_SIZE))"
```

### 5.8 镜像布局说明

```bash
# 最终镜像布局：
echo "=== zImage_signed.bin 布局 ==="
echo "1. zImage (原始kernel)     : 0x00000000 - 0x$(printf '%08X' $((KERNEL_SIZE - 1)))"
echo "2. Padding (4KB对齐)       : 0x$(printf '%08X' $KERNEL_SIZE) - 0x$(printf '%08X' $((KERNEL_ALIGNED - 1)))"
echo "3. IVT (32字节)            : 0x$(printf '%08X' $KERNEL_ALIGNED) - 0x$(printf '%08X' $((KERNEL_ALIGNED + 31)))"
echo "4. CSF (签名数据)          : 0x$(printf '%08X' $((KERNEL_ALIGNED + 32))) - 0x$(printf '%08X' $((SIGNED_SIZE - 1)))"

echo ""
echo "=== 重要说明 ==="
echo "- DTB文件保持独立，不合并到zImage_signed.bin中"
echo "- csf_zImage.bin包含了对kernel和DTB的统一签名信息"
echo "- 验证时需要同时加载zImage_signed.bin和DTB文件到指定内存地址"
```

## 6. 镜像烧录和验证

### 6.1 烧录签名镜像

```bash
# 烧录U-Boot到NAND Flash
=> nand erase 0x0 0x200000
=> tftp 0x80800000 u-boot-signed.imx
=> nand write 0x80800000 0x0 ${filesize}

# 烧录签名kernel到NAND Flash
=> nand erase 0x500000 0x800000
=> tftp 0x80800000 zImage_signed.bin
=> nand write 0x80800000 0x500000 ${filesize}

# 烧录DTB到NAND Flash（DTB保持原始文件）
=> nand erase 0x400000 0x100000
=> tftp 0x83000000 imx6ull-14x14-evk.dtb
=> nand write 0x83000000 0x400000 ${filesize}
```

### 6.2 U-Boot中加载和验证（基于实践验证）

```bash
# 从NAND加载签名kernel到内存
=> nand read 0x80800000 0x500000 0x800000

# 从NAND加载DTB到内存（原始DTB文件）
=> nand read 0x83000000 0x400000 0x100000

# 验证签名镜像（根据实践验证的参数）
=> hab_auth_img 0x80800000 0x66AF68 0x0066A000

# 检查HAB状态
=> hab_status

# 如果验证成功，启动系统
=> bootz 0x80800000 - 0x83000000
```

### 6.3 验证参数说明

```bash
# hab_auth_img参数解释（基于实践验证）：
# hab_auth_img 0x80800000 0x66AF68 0x0066A000
#
# 0x80800000: kernel加载地址
# 0x66AF68:   签名镜像总大小（zImage_pad_ivt.bin + csf_zImage.bin）
# 0x0066A000: IVT偏移（zImage_pad.bin的大小，即4KB对齐后的kernel大小）

echo "验证参数计算："
echo "Kernel对齐大小: 0x$(printf '%X' $KERNEL_ALIGNED)"
echo "IVT偏移:        0x$(printf '%X' $KERNEL_ALIGNED)"
echo "签名文件大小:   0x$(printf '%X' $SIGNED_SIZE)"
```

### 6.3 验证结果分析

#### 6.3.1 成功验证的输出
```
Authenticate image from DDR location 0x80800000...
Secure boot disabled
HAB Configuration: 0xf0, HAB State: 0x66
No HAB Events Found!
```

#### 6.3.2 失败验证的输出
```
HAB Configuration: 0xf0, HAB State: 0x66
--------- HAB Event 1 -----------------
STS = HAB_FAILURE (0x33)
RSN = HAB_INV_SIGNATURE (0x18)
CTX = HAB_CTX_COMMAND (0xC0)
```

## 7. 生产环境部署

### 7.1 SRK Hash烧录

```bash
# 计算SRK Hash
hexdump -e '/4 "0x"' -e '/4 "%X""\n"' SRK_1_2_3_4_fuse.bin

# 在U-Boot中烧录SRK Hash（谨慎操作！）
=> fuse prog 3 0 0x12345678  # SRK Hash[31:0]
=> fuse prog 3 1 0x9ABCDEF0  # SRK Hash[63:32]
=> fuse prog 3 2 0x11223344  # SRK Hash[95:64]
=> fuse prog 3 3 0x55667788  # SRK Hash[127:96]
=> fuse prog 3 4 0x99AABBCC  # SRK Hash[159:128]
=> fuse prog 3 5 0xDDEEFF00  # SRK Hash[191:160]
=> fuse prog 3 6 0x12345678  # SRK Hash[223:192]
=> fuse prog 3 7 0x9ABCDEF0  # SRK Hash[255:224]
```

### 7.2 启用安全启动

```bash
# 烧录SEC_CONFIG熔丝启用安全启动（不可逆操作！）
=> fuse prog 4 2 0x2  # 启用Secure Boot
```

## 8. 调试和故障排除

### 8.1 常见错误及解决方法

#### 8.1.1 HAB_INV_SIGNATURE (0x18)
**原因**：签名验证失败
**解决方法**：
- 检查证书链是否正确
- 验证CSF文件中的证书路径
- 确认私钥与证书匹配

#### 8.1.2 HAB_INV_ASSERTION (0x0C)
**原因**：地址断言失败
**解决方法**：
- 检查CSF中的地址配置
- 验证IVT偏移计算是否正确
- 确认加载地址与实际地址一致

#### 8.1.3 Data Abort
**原因**：内存访问越界
**解决方法**：
- 检查IVT偏移参数
- 验证镜像大小计算
- 确认内存地址范围

### 8.2 调试命令

```bash
# 查看HAB状态
=> hab_status

# 查看HAB事件
=> hab_failsafe

# 查看内存内容
=> md.b 0x80800000 0x100    # 查看镜像开头
=> md.b 0x80E6A000 0x40     # 查看IVT位置

# 查看熔丝状态
=> fuse read 3 0 8          # 读取SRK Hash
=> fuse read 4 2 1          # 读取SEC_CONFIG
```

## 9. 自动化脚本

### 9.1 基于实践验证的自动化脚本

```bash
#!/bin/bash
# sign_kernel_dtb_verified.sh - 基于实践验证的签名脚本

set -e

WORKSPACE=$(pwd)
CST_PATH="./cst"
KEYS_PATH="./keys"

echo "=== i.MX6ULL Kernel+DTB签名自动化脚本（实践验证版本） ==="

# 检查输入文件
if [ ! -f "zImage" ] || [ ! -f "imx6ull-14x14-evk.dtb" ]; then
    echo "错误：缺少zImage或DTB文件"
    exit 1
fi

# 计算镜像大小
KERNEL_SIZE=$(stat -c%s zImage)
KERNEL_ALIGNED=$(((KERNEL_SIZE + 4095) & ~4095))
DTB_SIZE=$(stat -c%s imx6ull-14x14-evk.dtb)

echo "Kernel原始大小: $KERNEL_SIZE"
echo "Kernel对齐大小: $KERNEL_ALIGNED (0x$(printf '%X' $KERNEL_ALIGNED))"
echo "DTB大小: $DTB_SIZE (0x$(printf '%X' $DTB_SIZE))"

# 步骤1：使用objcopy进行4KB对齐
echo "=== 步骤1：Kernel 4KB对齐 ==="
objcopy -I binary -O binary --pad-to $(printf "0x%X" $KERNEL_ALIGNED) --gap-fill=0x00 zImage zImage_pad.bin
echo "生成: zImage_pad.bin"

# 步骤2：生成IVT
echo "=== 步骤2：生成IVT ==="
perl genIVT.pl
echo "生成: ivt.bin"

# 步骤3：组合kernel和IVT
echo "=== 步骤3：组合kernel+IVT ==="
cat zImage_pad.bin ivt.bin > zImage_pad_ivt.bin
echo "生成: zImage_pad_ivt.bin"

# 步骤4：创建CSF文件
echo "=== 步骤4：创建CSF文件 ==="
create_csf_file() {
cat > csf_zImage.txt << EOF
[Header]
    Version = 4.2
    Hash Algorithm = sha256
    Engine Configuration = 0
    Certificate Format = X509
    Signature Format = CMS
    Engine = CAAM

[Install SRK]
    File = "SRK_1_2_3_4_table.bin"
    Source index = 0

[Install CSFK]
    File = "CSF1_1_sha256_4096_65537_v3_usr_crt.pem"

[Authenticate CSF]

[Install Key]
    Verification index = 0
    Target Index = 2
    File = "IMG1_1_sha256_4096_65537_v3_usr_crt.pem"

[Authenticate Data]
    Verification index = 2
    # 同时验证kernel和DTB，但保持为独立文件
    Blocks = 0x80800000 0x00000000 $(printf "0x%08X" $((KERNEL_ALIGNED + 0x20))) "zImage_pad_ivt.bin", \\
             0x83000000 0x00000000 $(printf "0x%08X" $DTB_SIZE) "imx6ull-14x14-evk.dtb"
EOF
}

create_csf_file
echo "生成: csf_zImage.txt"

# 步骤5：生成CSF签名
echo "=== 步骤5：生成CSF签名 ==="
$CST_PATH -i csf_zImage.txt -o csf_zImage.bin
echo "生成: csf_zImage.bin"

# 步骤6：创建最终签名镜像
echo "=== 步骤6：创建最终签名镜像 ==="
cat zImage_pad_ivt.bin csf_zImage.bin > zImage_signed.bin

# 计算最终大小
SIGNED_SIZE=$(stat -c%s zImage_signed.bin)

echo "=== 签名完成 ==="
echo "最终文件:"
echo "- zImage_signed.bin: $SIGNED_SIZE bytes (0x$(printf '%X' $SIGNED_SIZE))"
echo "- imx6ull-14x14-evk.dtb: $DTB_SIZE bytes (保持独立)"

# 显示验证命令
echo ""
echo "=== U-Boot验证命令 ==="
echo "# 加载签名kernel"
echo "nand read 0x80800000 0x500000 0x800000"
echo ""
echo "# 加载DTB"
echo "nand read 0x83000000 0x400000 0x100000"
echo ""
echo "# 验证签名（根据实际大小调整）"
echo "hab_auth_img 0x80800000 0x$(printf '%X' $SIGNED_SIZE) 0x$(printf '%X' $KERNEL_ALIGNED)"
echo ""
echo "# 启动系统"
echo "bootz 0x80800000 - 0x83000000"
```

## 10. 总结

本文档基于实际验证的方法，详细介绍了i.MX6ULL处理器HABv4安全启动的完整实现流程，采用**CSF统一签名验证kernel和DTB**的方式，包括：

### 🎯 核心特点（基于实践验证）

1. **CSF统一签名**：通过修改csf_additional_images.txt配置，一个CSF文件同时验证kernel和DTB
2. **文件独立性**：kernel和DTB保持为两个独立文件，便于单独更新
3. **实践验证**：所有参数和步骤都经过实际项目验证
4. **微信适配**：流程图适配微信公众号阅读格式，增加分叉显示

### 🔧 关键技术要点

- **objcopy对齐**：使用objcopy工具进行4KB对齐，确保精确填充
- **IVT生成**：genIVT.pl脚本生成正确的Image Vector Table
- **CSF Blocks配置**：通过Blocks参数同时验证两个独立文件
- **内存布局**：kernel加载到0x80800000，DTB加载到0x83000000

### 📋 实际验证的关键参数

```bash
# 基于实践验证的参数：
Load Address: 0x80800000          # kernel加载地址
DTB Address:  0x83000000          # DTB加载地址
IVT Offset:   0x66A000           # 根据实际kernel大小调整
验证命令:     hab_auth_img 0x80800000 0x66AF68 0x0066A000
```

### ⚠️ 重要注意事项

- **密钥安全**：妥善保管私钥和证书文件
- **熔丝操作**：SRK Hash和SEC_CONFIG烧录是不可逆操作
- **参数准确**：严格按照实际镜像大小计算验证参数
- **充分测试**：在开发环境充分验证后再部署生产

### 🚀 方案优势

相比其他签名方式，本方案具有以下优势：
- **简化验证**：一次hab_auth_img命令验证kernel和DTB
- **文件独立**：kernel和DTB可以单独更新，无需重新合并
- **减少错误**：避免复杂的镜像合并和地址计算错误
- **便于调试**：可以单独验证每个文件的签名正确性

### 📊 文件结构总结

```
最终生成的文件：
├── zImage_signed.bin          # 包含kernel+IVT+CSF的签名文件
├── imx6ull-14x14-evk.dtb     # 独立的DTB文件（原始）
└── csf_zImage.bin            # 包含对两个文件签名信息的CSF

烧录布局：
├── 0x000000: u-boot-signed.imx
├── 0x400000: imx6ull-14x14-evk.dtb
└── 0x500000: zImage_signed.bin
```

通过遵循本指南，您可以成功在i.MX6ULL平台上实现安全可靠的HABv4签名启动功能。

---

**📝 文档说明**：本文档严格基于"i.MX6-secure-boot-guide.txt"实践验证文档编写，确保所有步骤和参数的正确性。如有疑问，请参考NXP官方文档或联系技术支持。
```
