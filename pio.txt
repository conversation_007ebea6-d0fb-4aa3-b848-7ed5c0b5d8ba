PSTROS-2000/V2.0 # ./memtool ECSPI2.*
SOC: i.MX6ULL
ECSPI2   Addr:0x200c000 
  ECSPI2.RXDATA Addr:0x0200C000 Value:0x959F16F4 - The Receive Data register (ECSPI_RXDATA) is a read-only register that forms the top word of the 64 x 32 receive FIFO.
     ECSPI2.RXDATA.ECSPI_RXDATA(0..31)  :0x41713103
             Receive Data.

  ECSPI2.TXDATA Addr:0x0200C004 Value:0x00000000 - The Transmit Data (ECSPI_TXDATA) register is a write-only data register that forms the bottom word of the 64 x 32 TXFIFO.
     ECSPI2.TXDATA.ECSPI_TXDATA(0..31)  :0x0
             Transmit Data.

  ECSPI2.CONREG Addr:0x0200C008 Value:0xFFF010F1 - The Control Register (ECSPI_CONREG) allows software to enable the ECSPI , configure its operating modes, specify the divider value, and SPI_RDY control signal, and define the transfer length.
     ECSPI2.CONREG.EN(0..0)     :0x1
             SPI Block Enable Control.
     ECSPI2.CONREG.HT(1..1)     :0x0
             Hardware Trigger Enable.
     ECSPI2.CONREG.XCH(2..2)    :0x0
             SPI Exchange Bit.
     ECSPI2.CONREG.SMC(3..3)    :0x0
             Start Mode Control.
     ECSPI2.CONREG.CHANNEL_MODE(4..7)   :0xf
             SPI CHANNEL MODE selects the mode for each SPI channel.
     ECSPI2.CONREG.POST_DIVIDER(8..11)  :0x0
             SPI Post Divider.
     ECSPI2.CONREG.PRE_DIVIDER(12..15)  :0x1
             SPI Pre Divider.
     ECSPI2.CONREG.DRCTL(16..17)        :0x0
             SPI Data Ready Control.
     ECSPI2.CONREG.CHANNEL_SELECT(18..19)       :0x0
             SPI CHANNEL SELECT bits.
     ECSPI2.CONREG.BURST_LENGTH(20..31)         :0xfff
             Burst Length.

  ECSPI2.CONFIGREG Addr:0x0200C00C Value:0x00100111 - The Config Register (ECSPI_CONFIGREG) allows software to configure each SPI channel, configure its operating modes, specify the phase and polarity of the clock, configure the Chip Select (SS), and define the HT transfer length.
     ECSPI2.CONFIGREG.SCLK_PHA(0..3)    :0x1
             SPI Clock/Data Phase Control.
     ECSPI2.CONFIGREG.SCLK_POL(4..7)    :0x1
             SPI Clock Polarity Control.
     ECSPI2.CONFIGREG.SS_CTL(8..11)     :0x1
             SPI SS Wave Form Select.
     ECSPI2.CONFIGREG.SS_POL(12..15)    :0x0
             SPI SS Polarity Select.
     ECSPI2.CONFIGREG.DATA_CTL(16..19)  :0x0
             DATA CTL.
     ECSPI2.CONFIGREG.SCLK_CTL(20..23)  :0x1
             SCLK CTL.
     ECSPI2.CONFIGREG.HT_LENGTH(24..28)         :0x0
             HT LENGTH.

  ECSPI2.INTREG Addr:0x0200C010 Value:0x00000008 - The Interrupt Control Register (ECSPI_INTREG) enables the generation of interrupts to the host processor.
     ECSPI2.INTREG.TEEN(0..0)   :0x0
             TXFIFO Empty Interrupt enable.
     ECSPI2.INTREG.TDREN(1..1)  :0x0
             TXFIFO Data Request Interrupt enable.
     ECSPI2.INTREG.TFEN(2..2)   :0x0
             TXFIFO Full Interrupt enable.
     ECSPI2.INTREG.RREN(3..3)   :0x1
             RXFIFO Ready Interrupt enable.
     ECSPI2.INTREG.RDREN(4..4)  :0x0
             RXFIFO Data Request Interrupt enable.
     ECSPI2.INTREG.RFEN(5..5)   :0x0
             RXFIFO Full Interrupt enable.
     ECSPI2.INTREG.ROEN(6..6)   :0x0
             RXFIFO Overflow Interrupt enable.
     ECSPI2.INTREG.TCEN(7..7)   :0x0
             Transfer Completed Interrupt enable.

  ECSPI2.DMAREG Addr:0x0200C014 Value:0x00000000 - The Direct Memory Access Control Register (ECSPI_DMAREG) provides software a way to use an on-chip DMA controller for ECSPI data.
     ECSPI2.DMAREG.TX_THRESHOLD(0..5)   :0x0
             TX THRESHOLD.
     ECSPI2.DMAREG.TEDEN(7..7)  :0x0
             TXFIFO Empty DMA Request Enable.
     ECSPI2.DMAREG.RX_THRESHOLD(16..21)         :0x0
             RX THRESHOLD.
     ECSPI2.DMAREG.RXDEN(23..23)        :0x0
             RXFIFO DMA Request Enable.
     ECSPI2.DMAREG.RX_DMA_LENGTH(24..29)        :0x0
             RX DMA LENGTH.
     ECSPI2.DMAREG.RXTDEN(31..31)       :0x0
             RXFIFO TAIL DMA Request Enable.

  ECSPI2.STATREG Addr:0x0200C018 Value:0x00000083 - The ECSPI Status Register (ECSPI_STATREG) reflects the status of the ECSPI's operating condition.
     ECSPI2.STATREG.TE(0..0)    :0x1
             TXFIFO Empty.
     ECSPI2.STATREG.TDR(1..1)   :0x1
             TXFIFO Data Request.
     ECSPI2.STATREG.TF(2..2)    :0x0
             TXFIFO Full.
     ECSPI2.STATREG.RR(3..3)    :0x0
             RXFIFO Ready.
     ECSPI2.STATREG.RDR(4..4)   :0x0
             RXFIFO Data Request.
     ECSPI2.STATREG.RF(5..5)    :0x0
             RXFIFO Full.
     ECSPI2.STATREG.RO(6..6)    :0x0
             RXFIFO Overflow.
     ECSPI2.STATREG.TC(7..7)    :0x1
             Transfer Completed Status bit.

  ECSPI2.PERIODREG Addr:0x0200C01C Value:0x00000000 - The Sample Period Control Register (ECSPI_PERIODREG) provides software a way to insert delays (wait states) between consecutive SPI transfers.
     ECSPI2.PERIODREG.SAMPLE_PERIOD(0..14)      :0x0
             Sample Period Control.
     ECSPI2.PERIODREG.CSRC(15..15)      :0x0
             Clock Source Control.
     ECSPI2.PERIODREG.CSD_CTL(16..21)   :0x0
             Chip Select Delay Control bits.

  ECSPI2.TESTREG Addr:0x0200C020 Value:0x00000000 - The Test Control Register (ECSPI_TESTREG) provides software a mechanism to internally connect the receive and transmit devices of the ECSPI , and monitor the contents of the receive and transmit FIFOs.
     ECSPI2.TESTREG.TXCNT(0..6)         :0x0
             TXFIFO Counter.
     ECSPI2.TESTREG.RXCNT(8..14)        :0x0
             RXFIFO Counter.
     ECSPI2.TESTREG.LBC(31..31)         :0x0
             Loop Back Control.

  ECSPI2.MSGDATA Addr:0x0200C040 Value:0x00000000 - The Message Data Register (ECSPI_MSGDATA) forms the top word of the 16 x 32 MSG Data FIFO.
     ECSPI2.MSGDATA.ECSPI_MSGDATA(0..31)        :0x0
             ECSPI_MSGDATA holds the top word of MSG Data FIFO.

PSTROS-2000/V2.0 # ./memtool ECSPI2.*
SOC: i.MX6ULL
ECSPI2   Addr:0x200c000 
  ECSPI2.RXDATA Addr:0x0200C000 Value:0x959F16F4 - The Receive Data register (ECSPI_RXDATA) is a read-only register that forms the top word of the 64 x 32 receive FIFO.
     ECSPI2.RXDATA.ECSPI_RXDATA(0..31)  :0x41713103
             Receive Data.

  ECSPI2.TXDATA Addr:0x0200C004 Value:0x00000000 - The Transmit Data (ECSPI_TXDATA) register is a write-only data register that forms the bottom word of the 64 x 32 TXFIFO.
     ECSPI2.TXDATA.ECSPI_TXDATA(0..31)  :0x0
             Transmit Data.

  ECSPI2.CONREG Addr:0x0200C008 Value:0xFFF010F1 - The Control Register (ECSPI_CONREG) allows software to enable the ECSPI , configure its operating modes, specify the divider value, and SPI_RDY control signal, and define the transfer length.
     ECSPI2.CONREG.EN(0..0)     :0x1
             SPI Block Enable Control.
     ECSPI2.CONREG.HT(1..1)     :0x0
             Hardware Trigger Enable.
     ECSPI2.CONREG.XCH(2..2)    :0x0
             SPI Exchange Bit.
     ECSPI2.CONREG.SMC(3..3)    :0x0
             Start Mode Control.
     ECSPI2.CONREG.CHANNEL_MODE(4..7)   :0xf
             SPI CHANNEL MODE selects the mode for each SPI channel.
     ECSPI2.CONREG.POST_DIVIDER(8..11)  :0x0
             SPI Post Divider.
     ECSPI2.CONREG.PRE_DIVIDER(12..15)  :0x1
             SPI Pre Divider.
     ECSPI2.CONREG.DRCTL(16..17)        :0x0
             SPI Data Ready Control.
     ECSPI2.CONREG.CHANNEL_SELECT(18..19)       :0x0
             SPI CHANNEL SELECT bits.
     ECSPI2.CONREG.BURST_LENGTH(20..31)         :0xfff
             Burst Length.

  ECSPI2.CONFIGREG Addr:0x0200C00C Value:0x00100111 - The Config Register (ECSPI_CONFIGREG) allows software to configure each SPI channel, configure its operating modes, specify the phase and polarity of the clock, configure the Chip Select (SS), and define the HT transfer length.
     ECSPI2.CONFIGREG.SCLK_PHA(0..3)    :0x1
             SPI Clock/Data Phase Control.
     ECSPI2.CONFIGREG.SCLK_POL(4..7)    :0x1
             SPI Clock Polarity Control.
     ECSPI2.CONFIGREG.SS_CTL(8..11)     :0x1
             SPI SS Wave Form Select.
     ECSPI2.CONFIGREG.SS_POL(12..15)    :0x0
             SPI SS Polarity Select.
     ECSPI2.CONFIGREG.DATA_CTL(16..19)  :0x0
             DATA CTL.
     ECSPI2.CONFIGREG.SCLK_CTL(20..23)  :0x1
             SCLK CTL.
     ECSPI2.CONFIGREG.HT_LENGTH(24..28)         :0x0
             HT LENGTH.

  ECSPI2.INTREG Addr:0x0200C010 Value:0x00000008 - The Interrupt Control Register (ECSPI_INTREG) enables the generation of interrupts to the host processor.
     ECSPI2.INTREG.TEEN(0..0)   :0x0
             TXFIFO Empty Interrupt enable.
     ECSPI2.INTREG.TDREN(1..1)  :0x0
             TXFIFO Data Request Interrupt enable.
     ECSPI2.INTREG.TFEN(2..2)   :0x0
             TXFIFO Full Interrupt enable.
     ECSPI2.INTREG.RREN(3..3)   :0x1
             RXFIFO Ready Interrupt enable.
     ECSPI2.INTREG.RDREN(4..4)  :0x0
             RXFIFO Data Request Interrupt enable.
     ECSPI2.INTREG.RFEN(5..5)   :0x0
             RXFIFO Full Interrupt enable.
     ECSPI2.INTREG.ROEN(6..6)   :0x0
             RXFIFO Overflow Interrupt enable.
     ECSPI2.INTREG.TCEN(7..7)   :0x0
             Transfer Completed Interrupt enable.

  ECSPI2.DMAREG Addr:0x0200C014 Value:0x00000000 - The Direct Memory Access Control Register (ECSPI_DMAREG) provides software a way to use an on-chip DMA controller for ECSPI data.
     ECSPI2.DMAREG.TX_THRESHOLD(0..5)   :0x0
             TX THRESHOLD.
     ECSPI2.DMAREG.TEDEN(7..7)  :0x0
             TXFIFO Empty DMA Request Enable.
     ECSPI2.DMAREG.RX_THRESHOLD(16..21)         :0x0
             RX THRESHOLD.
     ECSPI2.DMAREG.RXDEN(23..23)        :0x0
             RXFIFO DMA Request Enable.
     ECSPI2.DMAREG.RX_DMA_LENGTH(24..29)        :0x0
             RX DMA LENGTH.
     ECSPI2.DMAREG.RXTDEN(31..31)       :0x0
             RXFIFO TAIL DMA Request Enable.

  ECSPI2.STATREG Addr:0x0200C018 Value:0x00000083 - The ECSPI Status Register (ECSPI_STATREG) reflects the status of the ECSPI's operating condition.
     ECSPI2.STATREG.TE(0..0)    :0x1
             TXFIFO Empty.
     ECSPI2.STATREG.TDR(1..1)   :0x1
             TXFIFO Data Request.
     ECSPI2.STATREG.TF(2..2)    :0x0
             TXFIFO Full.
     ECSPI2.STATREG.RR(3..3)    :0x0
             RXFIFO Ready.
     ECSPI2.STATREG.RDR(4..4)   :0x0
             RXFIFO Data Request.
     ECSPI2.STATREG.RF(5..5)    :0x0
             RXFIFO Full.
     ECSPI2.STATREG.RO(6..6)    :0x0
             RXFIFO Overflow.
     ECSPI2.STATREG.TC(7..7)    :0x1
             Transfer Completed Status bit.

  ECSPI2.PERIODREG Addr:0x0200C01C Value:0x00000000 - The Sample Period Control Register (ECSPI_PERIODREG) provides software a way to insert delays (wait states) between consecutive SPI transfers.
     ECSPI2.PERIODREG.SAMPLE_PERIOD(0..14)      :0x0
             Sample Period Control.
     ECSPI2.PERIODREG.CSRC(15..15)      :0x0
             Clock Source Control.
     ECSPI2.PERIODREG.CSD_CTL(16..21)   :0x0
             Chip Select Delay Control bits.

  ECSPI2.TESTREG Addr:0x0200C020 Value:0x00000000 - The Test Control Register (ECSPI_TESTREG) provides software a mechanism to internally connect the receive and transmit devices of the ECSPI , and monitor the contents of the receive and transmit FIFOs.
     ECSPI2.TESTREG.TXCNT(0..6)         :0x0
             TXFIFO Counter.
     ECSPI2.TESTREG.RXCNT(8..14)        :0x0
             RXFIFO Counter.
     ECSPI2.TESTREG.LBC(31..31)         :0x0
             Loop Back Control.

  ECSPI2.MSGDATA Addr:0x0200C040 Value:0x00000000 - The Message Data Register (ECSPI_MSGDATA) forms the top word of the 16 x 32 MSG Data FIFO.
     ECSPI2.MSGDATA.ECSPI_MSGDATA(0..31)        :0x0
             ECSPI_MSGDATA holds the top word of MSG Data FIFO.
