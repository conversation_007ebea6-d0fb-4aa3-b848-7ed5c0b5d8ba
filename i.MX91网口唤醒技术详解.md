# i.MX91网口唤醒技术详解

## 概述

Wake-on-LAN (WoL) 是一种网络标准，允许通过网络数据包远程唤醒处于睡眠或关机状态的设备。在NXP i.MX91处理器中，这项技术对于物联网和边缘计算应用尤为重要，可以实现设备的远程管理和节能控制。

## i.MX91硬件架构

### 以太网控制器

i.MX91集成了两个以太网控制器：

```mermaid
graph TD
    A[i.MX91 SoC] --> B[ENET Controller]
    A --> C[ENET_QOS Controller]

    B --> D[PHY Chip]
    C --> E[PHY Chip]

    D --> F[RJ45 Port - eth0]
    E --> G[RJ45 Port - eth1]

    style B fill:#e1f5fe
    style C fill:#f3e5f5
    style F fill:#c8e6c9
    style G fill:#ffcdd2
```

| 控制器 | 特性 | Wake-on-LAN支持 | Linux接口 |
|--------|------|-----------------|-----------|
| ENET | 10/100Mbps FEC | ✅ 支持 | eth0 |
| ENET_QOS | 10/100/1000Mbps QoS | ✅ 支持 | eth1 |

## Magic Packet技术原理

### Magic Packet帧结构

Magic Packet是一种特殊的以太网帧，用于唤醒支持Wake-on-LAN的设备：

```
┌─────────────────────────────────────────────────────────────────────┐
│                          以太网帧头部                                │
├─────────────────────┬─────────────────────┬─────────────────────────┤
│    目标MAC地址       │     源MAC地址        │      类型/长度          │
│     (6 bytes)       │     (6 bytes)       │      (2 bytes)          │
├─────────────────────┴─────────────────────┴─────────────────────────┤
│                        Magic Packet载荷                             │
├─────────────────────────────────────────────────────────────────────┤
│              同步序列: FF FF FF FF FF FF (6 bytes)                   │
├─────────────────────────────────────────────────────────────────────┤
│                目标设备MAC地址重复16次 (96 bytes)                     │
│    MAC MAC MAC MAC MAC MAC MAC MAC MAC MAC MAC MAC MAC MAC MAC MAC  │
├─────────────────────────────────────────────────────────────────────┤
│                     可选密码 (0, 4, 6 bytes)                         │
├─────────────────────────────────────────────────────────────────────┤
│                        以太网帧校验和                                │
│                         (4 bytes)                                   │
└─────────────────────────────────────────────────────────────────────┘
```

### Magic Packet详细结构

```mermaid
packet-beta
    0-47: "目标MAC地址"
    48-95: "源MAC地址"
    96-111: "EtherType"
    112-159: "同步序列 (6×0xFF)"
    160-927: "目标MAC×16次"
    928-959: "可选密码"
    960-991: "FCS校验"
```

### 载荷数据示例

假设目标设备MAC地址为 `00:1B:63:84:45:E6`：

```
同步序列: FF FF FF FF FF FF
MAC重复: 00 1B 63 84 45 E6 00 1B 63 84 45 E6 00 1B 63 84 45 E6 ...
         (重复16次，共96字节)
```

## i.MX91 Wake-on-LAN配置

### 硬件要求

1. **PHY芯片支持**：PHY必须支持Magic Packet检测
2. **电源管理**：3.3V辅助电源保持供电
3. **时钟源**：32kHz时钟保持运行

### 软件配置

#### 1. 检查硬件支持

```bash
# 检查网口Wake-on-LAN支持
ethtool eth0 | grep "Supports Wake-on"
ethtool eth1 | grep "Supports Wake-on"

# 输出示例：
# Supports Wake-on: pumbg
# p - PHY activity
# u - Unicast messages
# m - Multicast messages
# b - Broadcast messages
# g - Magic packet
```

#### 2. 启用Wake-on-LAN

```bash
# 对于ENET控制器 (eth0)
ethtool -s eth0 wol g

# 对于ENET_QOS控制器 (eth1)
ethtool -s eth1 wol g

# 验证配置
ethtool eth0 | grep "Wake-on"
ethtool eth1 | grep "Wake-on"
```

#### 3. 配置电源管理

```bash
# 启用网口作为唤醒源
echo enabled > /sys/class/net/eth0/device/power/wakeup
echo enabled > /sys/class/net/eth1/device/power/wakeup

# 保持网口电源
echo on > /sys/class/net/eth0/device/power/control
echo on > /sys/class/net/eth1/device/power/control
```

### 持久化配置

#### udev规则配置

```bash
# 创建udev规则文件
cat > /etc/udev/rules.d/99-wol-enable.rules << EOF
# 自动启用Wake-on-LAN
SUBSYSTEM=="net", ACTION=="add", DRIVERS=="fec", RUN+="/sbin/ethtool -s %k wol g"
SUBSYSTEM=="net", ACTION=="add", DRIVERS=="dwmac-imx", RUN+="/sbin/ethtool -s %k wol g"

# 配置电源管理
SUBSYSTEM=="net", ACTION=="add", DRIVERS=="fec", ATTR{power/wakeup}="enabled"
SUBSYSTEM=="net", ACTION=="add", DRIVERS=="dwmac-imx", ATTR{power/wakeup}="enabled"
EOF

# 重新加载规则
udevadm control --reload-rules
udevadm trigger
```

#### systemd服务配置

```bash
# 创建Wake-on-LAN服务
cat > /etc/systemd/system/wol-enable.service << EOF
[Unit]
Description=Enable Wake-on-LAN
After=network.target

[Service]
Type=oneshot
ExecStart=/bin/bash -c 'for iface in eth0 eth1; do if [ -e /sys/class/net/\$iface ]; then ethtool -s \$iface wol g; echo enabled > /sys/class/net/\$iface/device/power/wakeup; fi; done'
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
EOF

# 启用服务
systemctl enable wol-enable.service
systemctl start wol-enable.service
```

## 深度睡眠模式配置

### i.MX91电源模式

```mermaid
stateDiagram-v2
    [*] --> Run: 系统启动
    Run --> Idle: CPU空闲
    Idle --> Run: 中断唤醒
    Run --> Suspend: 进入睡眠
    Suspend --> Run: Wake-on-LAN唤醒
    Run --> DeepSleep: 深度睡眠
    DeepSleep --> Run: Magic Packet唤醒

    note right of Suspend
        RAM保持供电
        网口保持最小功耗
    end note

    note right of DeepSleep
        仅关键模块供电
        PHY保持Magic Packet检测
    end note
```

### 进入深度睡眠

```bash
# 配置唤醒源
echo enabled > /sys/class/net/eth0/device/power/wakeup

# 进入suspend-to-RAM模式
echo mem > /sys/power/state

# 或使用systemd
systemctl suspend
```

## Magic Packet发送

### 使用wakeonlan工具

```bash
# 安装wakeonlan工具
sudo apt-get install wakeonlan  # Ubuntu/Debian
sudo yum install wakeonlan      # CentOS/RHEL

# 获取目标设备MAC地址
TARGET_MAC=$(ip link show eth0 | grep -o -E '([[:xdigit:]]{1,2}:){5}[[:xdigit:]]{1,2}')

# 发送Magic Packet
wakeonlan $TARGET_MAC

# 指定网络接口发送
wakeonlan -i eth0 $TARGET_MAC

# 指定端口发送
wakeonlan -p 9 $TARGET_MAC
```

### Python实现Magic Packet发送

```python
#!/usr/bin/env python3
import socket
import struct
import argparse

def send_magic_packet(mac_address, broadcast_ip='***************', port=9):
    """
    发送Magic Packet唤醒指定MAC地址的设备

    Args:
        mac_address: 目标设备MAC地址 (格式: XX:XX:XX:XX:XX:XX)
        broadcast_ip: 广播IP地址
        port: UDP端口号
    """
    # 移除MAC地址分隔符并转换为字节
    mac_bytes = bytes.fromhex(mac_address.replace(':', '').replace('-', ''))

    # 构造Magic Packet
    # 6个0xFF字节 + 16次重复的MAC地址
    magic_packet = b'\xff' * 6 + mac_bytes * 16

    # 创建UDP socket并发送
    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    sock.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)

    try:
        sock.sendto(magic_packet, (broadcast_ip, port))
        print(f"Magic Packet sent to {mac_address}")
        print(f"Packet size: {len(magic_packet)} bytes")
        print(f"Target: {broadcast_ip}:{port}")
    except Exception as e:
        print(f"Error sending Magic Packet: {e}")
    finally:
        sock.close()

def main():
    parser = argparse.ArgumentParser(description='Send Magic Packet for Wake-on-LAN')
    parser.add_argument('mac', help='Target MAC address (XX:XX:XX:XX:XX:XX)')
    parser.add_argument('-i', '--ip', default='***************',
                       help='Broadcast IP address (default: ***************)')
    parser.add_argument('-p', '--port', type=int, default=9,
                       help='UDP port (default: 9)')

    args = parser.parse_args()
    send_magic_packet(args.mac, args.ip, args.port)

if __name__ == "__main__":
    main()
```

### C语言实现

```c
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>

int send_magic_packet(const char* mac_str, const char* broadcast_ip, int port) {
    unsigned char mac[6];
    unsigned char packet[102]; // 6 + 96 bytes
    struct sockaddr_in addr;
    int sock, i;

    // 解析MAC地址
    if (sscanf(mac_str, "%hhx:%hhx:%hhx:%hhx:%hhx:%hhx",
               &mac[0], &mac[1], &mac[2], &mac[3], &mac[4], &mac[5]) != 6) {
        printf("Invalid MAC address format\n");
        return -1;
    }

    // 构造Magic Packet
    memset(packet, 0xFF, 6); // 同步序列
    for (i = 0; i < 16; i++) {
        memcpy(packet + 6 + i * 6, mac, 6); // 重复MAC地址16次
    }

    // 创建socket
    sock = socket(AF_INET, SOCK_DGRAM, 0);
    if (sock < 0) {
        perror("socket");
        return -1;
    }

    // 启用广播
    int broadcast = 1;
    setsockopt(sock, SOL_SOCKET, SO_BROADCAST, &broadcast, sizeof(broadcast));

    // 设置目标地址
    memset(&addr, 0, sizeof(addr));
    addr.sin_family = AF_INET;
    addr.sin_port = htons(port);
    inet_pton(AF_INET, broadcast_ip, &addr.sin_addr);

    // 发送Magic Packet
    if (sendto(sock, packet, sizeof(packet), 0,
               (struct sockaddr*)&addr, sizeof(addr)) < 0) {
        perror("sendto");
        close(sock);
        return -1;
    }

    printf("Magic Packet sent to %s\n", mac_str);
    close(sock);
    return 0;
}

int main(int argc, char* argv[]) {
    if (argc < 2) {
        printf("Usage: %s <MAC_ADDRESS> [BROADCAST_IP] [PORT]\n", argv[0]);
        return 1;
    }

    const char* mac = argv[1];
    const char* ip = argc > 2 ? argv[2] : "***************";
    int port = argc > 3 ? atoi(argv[3]) : 9;

    return send_magic_packet(mac, ip, port);
}
```

## 测试和验证

### 功能测试流程

```mermaid
sequenceDiagram
    participant Host as 发送主机
    participant Switch as 网络交换机
    participant Target as i.MX91目标设备
    participant PHY as PHY芯片

    Note over Target: 配置Wake-on-LAN
    Target->>Target: ethtool -s eth0 wol g
    Target->>Target: echo mem > /sys/power/state
    Note over Target: 进入深度睡眠

    Host->>Host: wakeonlan MAC_ADDRESS
    Host->>Switch: Magic Packet
    Switch->>PHY: 转发Magic Packet
    PHY->>PHY: 检测Magic Packet
    PHY->>Target: 发送唤醒信号
    Target->>Target: 系统唤醒
    Note over Target: 恢复正常运行
```

### 测试脚本

```bash
#!/bin/bash
# i.MX91 Wake-on-LAN测试脚本

TARGET_MAC="00:1B:63:84:45:E6"  # 替换为实际MAC地址
TEST_HOST="*************"       # 测试主机IP

echo "=== i.MX91 Wake-on-LAN测试 ==="

# 1. 检查网口状态
echo "1. 检查网口Wake-on-LAN支持:"
for iface in eth0 eth1; do
    if [ -e /sys/class/net/$iface ]; then
        echo "  $iface:"
        ethtool $iface | grep -E "(Wake-on|Link detected)"
    fi
done

# 2. 配置Wake-on-LAN
echo "2. 配置Wake-on-LAN:"
for iface in eth0 eth1; do
    if [ -e /sys/class/net/$iface ]; then
        ethtool -s $iface wol g
        echo enabled > /sys/class/net/$iface/device/power/wakeup
        echo "  $iface: Wake-on-LAN已启用"
    fi
done

# 3. 测试网络连通性
echo "3. 测试网络连通性:"
if ping -c 3 $TEST_HOST > /dev/null 2>&1; then
    echo "  网络连通正常"
else
    echo "  警告: 无法连接到测试主机 $TEST_HOST"
fi

# 4. 显示当前配置
echo "4. 当前配置:"
echo "  目标MAC地址: $TARGET_MAC"
echo "  Magic Packet发送命令: wakeonlan $TARGET_MAC"

# 5. 准备进入睡眠模式
echo "5. 准备测试:"
echo "  请在另一台主机上运行: wakeonlan $TARGET_MAC"
echo "  然后按Enter键进入睡眠模式..."
read -p "  按Enter继续..."

echo "  系统将在5秒后进入睡眠模式..."
sleep 5
echo mem > /sys/power/state
```

### 性能测试

```bash
#!/bin/bash
# Wake-on-LAN性能测试

TARGET_MAC="00:1B:63:84:45:E6"
LOG_FILE="/tmp/wol_performance.log"

echo "Wake-on-LAN性能测试 - $(date)" > $LOG_FILE

for i in {1..10}; do
    echo "测试轮次 $i:" | tee -a $LOG_FILE

    # 记录睡眠时间
    SLEEP_TIME=$(date +%s.%N)
    echo "进入睡眠: $(date)" | tee -a $LOG_FILE

    # 进入睡眠模式
    echo mem > /sys/power/state

    # 系统唤醒后记录时间
    WAKE_TIME=$(date +%s.%N)
    echo "系统唤醒: $(date)" | tee -a $LOG_FILE

    # 计算唤醒时间
    WAKE_DURATION=$(echo "$WAKE_TIME - $SLEEP_TIME" | bc -l)
    echo "唤醒耗时: ${WAKE_DURATION}秒" | tee -a $LOG_FILE

    # 等待网络恢复
    while ! ping -c 1 ******* > /dev/null 2>&1; do
        sleep 0.1
    done

    NETWORK_TIME=$(date +%s.%N)
    NETWORK_DURATION=$(echo "$NETWORK_TIME - $WAKE_TIME" | bc -l)
    echo "网络恢复耗时: ${NETWORK_DURATION}秒" | tee -a $LOG_FILE
    echo "---" | tee -a $LOG_FILE

    sleep 10  # 等待系统稳定
done

echo "测试完成，详细日志: $LOG_FILE"
```

## 故障排除

### 常见问题及解决方案

#### 1. Magic Packet无法唤醒设备

**可能原因：**
- PHY芯片不支持Wake-on-LAN
- 电源管理配置错误
- 网络交换机过滤Magic Packet

**解决方案：**
```bash
# 检查PHY支持
ethtool eth0 | grep "Supports Wake-on"

# 检查当前配置
ethtool eth0 | grep "Wake-on"

# 重新配置
ethtool -s eth0 wol g
echo enabled > /sys/class/net/eth0/device/power/wakeup

# 检查电源域
cat /sys/class/net/eth0/device/power/control
```

#### 2. 唤醒后网络不可用

**可能原因：**
- 网络驱动未正确恢复
- IP地址配置丢失
- 路由表清空

**解决方案：**
```bash
# 创建网络恢复脚本
cat > /etc/systemd/system/network-resume.service << EOF
[Unit]
Description=Network Resume Service
After=suspend.target

[Service]
Type=oneshot
ExecStart=/bin/bash -c 'sleep 2; systemctl restart networking'

[Install]
WantedBy=suspend.target
EOF

systemctl enable network-resume.service
```

#### 3. 功耗过高

**可能原因：**
- 网口保持全功率运行
- PHY配置不当

**解决方案：**
```bash
# 配置网口低功耗模式
ethtool -s eth0 speed 100 duplex full autoneg off
ethtool --set-eee eth0 eee on

# 检查功耗状态
cat /sys/class/net/eth0/device/power/runtime_status
```

### 调试工具

#### 网络包捕获

```bash
# 捕获Magic Packet
tcpdump -i eth0 -X 'ether[0:4] = 0xffffffff and ether[4:2] = 0xffff'

# 分析Magic Packet内容
tcpdump -i eth0 -s 0 -w magic_packet.pcap
wireshark magic_packet.pcap
```

#### 系统日志分析

```bash
# 查看唤醒相关日志
journalctl -u systemd-suspend.service
dmesg | grep -i "wake\|suspend\|ethernet"

# 实时监控电源事件
tail -f /var/log/syslog | grep -i "wake\|suspend"
```

## 最佳实践

### 1. 安全考虑

```bash
# 启用SecureOn (密码保护)
ethtool -s eth0 wol gs sopass 00:11:22:33:44:55

# 限制唤醒源
echo 'SUBSYSTEM=="net", ATTR{address}=="specific:mac:addr", ATTR{power/wakeup}="enabled"' > /etc/udev/rules.d/99-wol-secure.rules
```

### 2. 网络配置优化

```bash
# 配置静态IP避免DHCP延迟
cat > /etc/systemd/network/eth0.network << EOF
[Match]
Name=eth0

[Network]
Address=*************/24
Gateway=***********
DNS=*******
EOF
```

### 3. 电源管理优化

```bash
# 配置运行时电源管理
echo auto > /sys/class/net/eth0/device/power/control

# 设置唤醒延迟
echo 1000 > /sys/class/net/eth0/device/power/autosuspend_delay_ms
```

## 应用场景

### 1. 物联网设备管理

```mermaid
graph TD
    A[管理中心] --> B[路由器/交换机]
    B --> C[i.MX91设备1]
    B --> D[i.MX91设备2]
    B --> E[i.MX91设备N]

    C --> F[传感器数据采集]
    D --> G[边缘计算处理]
    E --> H[设备控制执行]

    style A fill:#e1f5fe
    style C fill:#f3e5f5
    style D fill:#f3e5f5
    style E fill:#f3e5f5
```

**应用优势：**
- 远程设备唤醒和管理
- 降低整体系统功耗
- 提高设备可维护性

### 2. 边缘计算节点

```bash
# 智能唤醒脚本示例
#!/bin/bash
# 根据任务需求智能唤醒边缘节点

EDGE_NODES=("***********01" "***********02" "*************")
NODE_MACS=("00:1B:63:84:45:E6" "00:1B:63:84:45:E7" "00:1B:63:84:45:E8")

# 检查计算任务队列
TASK_COUNT=$(redis-cli llen compute_tasks)

if [ $TASK_COUNT -gt 10 ]; then
    echo "高负载，唤醒所有节点"
    for mac in "${NODE_MACS[@]}"; do
        wakeonlan $mac
    done
elif [ $TASK_COUNT -gt 5 ]; then
    echo "中等负载，唤醒部分节点"
    wakeonlan ${NODE_MACS[0]}
    wakeonlan ${NODE_MACS[1]}
else
    echo "低负载，保持当前状态"
fi
```

## 性能指标

### Wake-on-LAN性能对比

| 指标 | i.MX91 ENET | i.MX91 ENET_QOS | 传统方案 |
|------|-------------|-----------------|----------|
| 唤醒时间 | 1-2秒 | 0.5-1秒 | 3-5秒 |
| 睡眠功耗 | 2-5mW | 3-8mW | 50-100mW |
| 网络恢复 | 0.5秒 | 0.2秒 | 2-3秒 |
| 可靠性 | 99.5% | 99.8% | 95% |

### 功耗分析

```mermaid
pie title i.MX91睡眠模式功耗分布
    "CPU核心" : 0.1
    "RAM保持" : 1.5
    "网口PHY" : 2.0
    "时钟系统" : 0.5
    "其他外设" : 0.9
```

## 总结

i.MX91的Wake-on-LAN功能为物联网和边缘计算应用提供了强大的远程管理能力。通过合理配置硬件和软件，可以实现：

- **低功耗运行**：深度睡眠模式下功耗可降至毫瓦级别
- **快速唤醒**：Magic Packet唤醒时间通常在1-3秒内
- **可靠性**：支持多种唤醒源和故障恢复机制
- **安全性**：可配置密码保护和访问控制

正确实施Wake-on-LAN技术，可以显著提升i.MX91设备的能效比和管理便利性，是现代嵌入式系统设计的重要组成部分。

### 关键要点

1. **硬件配置**：确保PHY芯片支持Magic Packet检测
2. **软件配置**：正确设置ethtool和电源管理参数
3. **网络环境**：确保交换机不过滤Magic Packet
4. **安全考虑**：使用密码保护和访问控制
5. **性能优化**：根据应用场景调整功耗和响应时间平衡

通过本文的详细介绍和实践指导，开发者可以充分利用i.MX91的Wake-on-LAN功能，构建高效、可靠的物联网和边缘计算解决方案。
