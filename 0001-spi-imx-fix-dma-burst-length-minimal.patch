From: Developer <<EMAIL>>
Date: Thu, 5 Sep 2025 10:00:00 +0800
Subject: [PATCH] spi: imx: fix DMA burst length for continuous transfer

Fix ECSPI DMA mode byte interval issue by setting appropriate burst length.
The original implementation uses default burst length of 8 bits (1 byte) 
for DMA transfers, causing CS switching after every single byte and 
creating transmission gaps.

Root cause:
- DMA mode: BURST_LENGTH defaults to 8 bits (1 byte)
- PIO mode: BURST_LENGTH set to 4096 bits (512 bytes)
- BURST_LENGTH unit is bits, not bytes

Solution:
Set BURST_LENGTH to 4096 bits (512 bytes) for DMA mode to enable
continuous transfer without byte intervals.

This minimal fix only modifies register configuration without changing
driver logic, ensuring maximum compatibility and minimum risk.

Tested on i.MX6ULL with 4.19.35 kernel.

Signed-off-by: Developer <<EMAIL>>
---
 drivers/spi/spi-imx.c | 8 ++++++++
 1 file changed, 8 insertions(+)

diff --git a/drivers/spi/spi-imx.c b/drivers/spi/spi-imx.c
index 0b9531afed0e..fixed 100644
--- a/drivers/spi/spi-imx.c
+++ b/drivers/spi/spi-imx.c
@@ -607,6 +607,14 @@ static int mx51_ecspi_config(struct spi_device *spi)
 	/* set chip select to use */
 	ctrl |= MX51_ECSPI_CTRL_CS(spi->chip_select);
 
+	/* Fix DMA burst length for continuous transfer */
+	if (spi_imx->usedma) {
+		/* Clear existing burst length */
+		ctrl &= ~MX51_ECSPI_CTRL_BL_MASK;
+		/* Set burst length to 4096 bits (512 bytes) for continuous transfer */
+		ctrl |= (4095 << MX51_ECSPI_CTRL_BL_OFFSET);
+	}
+
 	/*
 	 * eCSPI burst completion by Chip Select signal in Slave mode
 	 * is not functional for imx53 Soc, config SPI burst completed when
-- 
2.25.1
