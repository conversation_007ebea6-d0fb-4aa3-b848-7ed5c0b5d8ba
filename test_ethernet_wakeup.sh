#!/bin/bash

# i.MX91 网口唤醒测试脚本
# 用于测试和验证网口快速唤醒优化配置

SCRIPT_NAME="i.MX91 Ethernet Wakeup Test"
ETH_INTERFACE="eth0"
TEST_HOST="*******"  # Google DNS，用于测试网络连通性
LOG_FILE="/tmp/ethernet_wakeup_test.log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a $LOG_FILE
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a $LOG_FILE
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a $LOG_FILE
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a $LOG_FILE
}

log_test() {
    echo -e "${CYAN}[TEST]${NC} $1" | tee -a $LOG_FILE
}

# 初始化测试环境
init_test() {
    echo "========================================" | tee $LOG_FILE
    echo "$SCRIPT_NAME" | tee -a $LOG_FILE
    echo "测试时间: $(date)" | tee -a $LOG_FILE
    echo "========================================" | tee -a $LOG_FILE
    
    log_info "初始化测试环境..."
    
    # 检查网口是否存在
    if ! ip link show $ETH_INTERFACE &>/dev/null; then
        log_error "网口 $ETH_INTERFACE 不存在"
        exit 1
    fi
    
    # 检查必要工具
    for tool in ethtool ping systemctl; do
        if ! command -v $tool &> /dev/null; then
            log_error "缺少必要工具: $tool"
            exit 1
        fi
    done
    
    log_success "测试环境初始化完成"
}

# 测试1：检查当前网口状态
test_current_status() {
    log_test "测试1: 检查当前网口状态"
    
    # 网口基本信息
    log_info "网口基本信息:"
    ip link show $ETH_INTERFACE | tee -a $LOG_FILE
    
    # MAC地址
    MAC_ADDR=$(ip link show $ETH_INTERFACE | grep -o -E '([[:xdigit:]]{1,2}:){5}[[:xdigit:]]{1,2}')
    log_info "MAC地址: $MAC_ADDR"
    
    # ethtool信息
    log_info "ethtool详细信息:"
    ethtool $ETH_INTERFACE | tee -a $LOG_FILE
    
    # Wake-on-LAN状态
    WOL_STATUS=$(ethtool $ETH_INTERFACE | grep "Wake-on:" | awk '{print $2}')
    log_info "当前Wake-on-LAN状态: $WOL_STATUS"
    
    # 电源管理状态
    if [[ -f /sys/class/net/$ETH_INTERFACE/device/power/wakeup ]]; then
        WAKEUP_STATUS=$(cat /sys/class/net/$ETH_INTERFACE/device/power/wakeup)
        log_info "Wakeup状态: $WAKEUP_STATUS"
    fi
    
    if [[ -f /sys/class/net/$ETH_INTERFACE/device/power/control ]]; then
        CONTROL_STATUS=$(cat /sys/class/net/$ETH_INTERFACE/device/power/control)
        log_info "Power Control状态: $CONTROL_STATUS"
    fi
}

# 测试2：基准网络性能测试
test_baseline_performance() {
    log_test "测试2: 基准网络性能测试"
    
    # 确保网口是up状态
    ip link set $ETH_INTERFACE up
    sleep 2
    
    # 测试ping延迟（5次）
    log_info "测试ping延迟到 $TEST_HOST (5次):"
    for i in {1..5}; do
        PING_TIME=$(ping -c 1 -W 3 $TEST_HOST 2>/dev/null | grep "time=" | grep -o "time=[0-9.]*" | cut -d= -f2)
        if [[ -n $PING_TIME ]]; then
            log_info "第${i}次: ${PING_TIME}ms"
        else
            log_warning "第${i}次: 超时"
        fi
        sleep 1
    done
    
    # 测试网口启动时间
    log_info "测试网口重启时间:"
    ip link set $ETH_INTERFACE down
    sleep 1
    
    START_TIME=$(date +%s.%N)
    ip link set $ETH_INTERFACE up
    
    # 等待链路建立
    while true; do
        if ethtool $ETH_INTERFACE | grep -q "Link detected: yes"; then
            END_TIME=$(date +%s.%N)
            LINK_TIME=$(echo "$END_TIME - $START_TIME" | bc -l)
            log_info "链路建立时间: ${LINK_TIME}秒"
            break
        fi
        sleep 0.1
    done
    
    # 等待网络可用
    START_TIME=$(date +%s.%N)
    while true; do
        if ping -c 1 -W 1 $TEST_HOST &>/dev/null; then
            END_TIME=$(date +%s.%N)
            NETWORK_TIME=$(echo "$END_TIME - $START_TIME" | bc -l)
            log_info "网络可用时间: ${NETWORK_TIME}秒"
            break
        fi
        sleep 0.1
    done
}

# 测试3：应用优化配置
test_apply_optimization() {
    log_test "测试3: 应用优化配置"
    
    # 备份当前配置
    log_info "备份当前配置..."
    BACKUP_WOL=$(ethtool $ETH_INTERFACE | grep "Wake-on:" | awk '{print $2}')
    
    # 应用优化配置
    log_info "应用优化配置..."
    
    # 1. 启用Wake-on-LAN
    if ethtool -s $ETH_INTERFACE wol g; then
        log_success "Wake-on-LAN配置成功"
    else
        log_error "Wake-on-LAN配置失败"
    fi
    
    # 2. 配置电源管理
    if echo enabled > /sys/class/net/$ETH_INTERFACE/device/power/wakeup 2>/dev/null; then
        log_success "Wakeup配置成功"
    else
        log_warning "Wakeup配置失败（可能需要root权限）"
    fi
    
    if echo on > /sys/class/net/$ETH_INTERFACE/device/power/control 2>/dev/null; then
        log_success "Power Control配置成功"
    else
        log_warning "Power Control配置失败（可能需要root权限）"
    fi
    
    # 验证配置
    log_info "验证优化配置:"
    NEW_WOL=$(ethtool $ETH_INTERFACE | grep "Wake-on:" | awk '{print $2}')
    log_info "新的Wake-on-LAN状态: $NEW_WOL"
    
    if [[ -f /sys/class/net/$ETH_INTERFACE/device/power/wakeup ]]; then
        NEW_WAKEUP=$(cat /sys/class/net/$ETH_INTERFACE/device/power/wakeup)
        log_info "新的Wakeup状态: $NEW_WAKEUP"
    fi
}

# 测试4：模拟suspend/resume测试
test_suspend_resume_simulation() {
    log_test "测试4: 模拟suspend/resume测试"
    
    log_info "模拟网口断电重连..."
    
    # 记录开始时间
    START_TIME=$(date +%s.%N)
    
    # 模拟suspend：关闭网口
    ip link set $ETH_INTERFACE down
    sleep 2
    
    # 模拟resume：重新启用网口
    ip link set $ETH_INTERFACE up
    
    # 测量链路恢复时间
    while true; do
        if ethtool $ETH_INTERFACE | grep -q "Link detected: yes"; then
            LINK_END_TIME=$(date +%s.%N)
            LINK_RECOVERY_TIME=$(echo "$LINK_END_TIME - $START_TIME" | bc -l)
            log_info "优化后链路恢复时间: ${LINK_RECOVERY_TIME}秒"
            break
        fi
        sleep 0.1
    done
    
    # 测量网络连通恢复时间
    while true; do
        if ping -c 1 -W 1 $TEST_HOST &>/dev/null; then
            NETWORK_END_TIME=$(date +%s.%N)
            NETWORK_RECOVERY_TIME=$(echo "$NETWORK_END_TIME - $START_TIME" | bc -l)
            log_info "优化后网络连通恢复时间: ${NETWORK_RECOVERY_TIME}秒"
            break
        fi
        sleep 0.1
    done
}

# 测试5：Magic Packet测试准备
test_magic_packet_preparation() {
    log_test "测试5: Magic Packet测试准备"
    
    MAC_ADDR=$(ip link show $ETH_INTERFACE | grep -o -E '([[:xdigit:]]{1,2}:){5}[[:xdigit:]]{1,2}')
    log_info "设备MAC地址: $MAC_ADDR"
    
    # 检查wakeonlan工具
    if command -v wakeonlan &> /dev/null; then
        log_success "wakeonlan工具已安装"
        log_info "Magic Packet测试命令: wakeonlan $MAC_ADDR"
    else
        log_warning "wakeonlan工具未安装"
        log_info "安装命令: sudo apt-get install wakeonlan"
        log_info "或者: sudo yum install wakeonlan"
    fi
    
    # 生成Python版本的Magic Packet发送脚本
    cat > /tmp/send_magic_packet.py << EOF
#!/usr/bin/env python3
import socket
import struct

def send_magic_packet(mac_address, broadcast_ip='***************', port=9):
    # 移除MAC地址中的分隔符并转换为字节
    mac_bytes = bytes.fromhex(mac_address.replace(':', '').replace('-', ''))
    
    # 构造Magic Packet: 6个0xFF + 16次重复的MAC地址
    magic_packet = b'\xff' * 6 + mac_bytes * 16
    
    # 发送UDP广播包
    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    sock.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
    sock.sendto(magic_packet, (broadcast_ip, port))
    sock.close()
    
    print(f"Magic Packet sent to {mac_address}")

if __name__ == "__main__":
    send_magic_packet("$MAC_ADDR")
EOF
    
    chmod +x /tmp/send_magic_packet.py
    log_info "Python Magic Packet脚本已生成: /tmp/send_magic_packet.py"
}

# 生成测试报告
generate_report() {
    log_test "生成测试报告"
    
    REPORT_FILE="/tmp/ethernet_wakeup_test_report.md"
    
    cat > $REPORT_FILE << EOF
# i.MX91 网口唤醒测试报告

## 测试概要
- 测试时间: $(date)
- 测试设备: i.MX91
- 网口接口: $ETH_INTERFACE
- 测试主机: $TEST_HOST

## 测试结果

### 当前配置状态
- MAC地址: $(ip link show $ETH_INTERFACE | grep -o -E '([[:xdigit:]]{1,2}:){5}[[:xdigit:]]{1,2}')
- Wake-on-LAN: $(ethtool $ETH_INTERFACE | grep "Wake-on:" | awk '{print $2}')
- 链路状态: $(ethtool $ETH_INTERFACE | grep "Link detected:" | awk '{print $3}')

### 性能测试结果
详细结果请查看: $LOG_FILE

### 建议操作
1. 如果测试通过，可以进行实际的suspend/resume测试
2. 使用以下命令进行真实测试：
   \`\`\`bash
   # 进入待机模式
   sudo systemctl suspend
   
   # 唤醒后测试网络恢复时间
   time ping -c 1 $TEST_HOST
   \`\`\`

### Magic Packet测试
- 从其他设备发送: \`wakeonlan $(ip link show $ETH_INTERFACE | grep -o -E '([[:xdigit:]]{1,2}:){5}[[:xdigit:]]{1,2}')\`
- 或使用Python脚本: \`python3 /tmp/send_magic_packet.py\`

EOF
    
    log_success "测试报告已生成: $REPORT_FILE"
}

# 主测试流程
main() {
    case "${1:-all}" in
        all)
            init_test
            test_current_status
            test_baseline_performance
            test_apply_optimization
            test_suspend_resume_simulation
            test_magic_packet_preparation
            generate_report
            ;;
        status)
            init_test
            test_current_status
            ;;
        baseline)
            init_test
            test_baseline_performance
            ;;
        optimize)
            init_test
            test_apply_optimization
            ;;
        simulate)
            init_test
            test_suspend_resume_simulation
            ;;
        magic)
            init_test
            test_magic_packet_preparation
            ;;
        *)
            echo "用法: $0 [all|status|baseline|optimize|simulate|magic]"
            echo ""
            echo "  all      - 运行所有测试（默认）"
            echo "  status   - 检查当前状态"
            echo "  baseline - 基准性能测试"
            echo "  optimize - 应用优化配置"
            echo "  simulate - 模拟suspend/resume"
            echo "  magic    - Magic Packet测试准备"
            ;;
    esac
}

# 检查bc工具（用于浮点计算）
if ! command -v bc &> /dev/null; then
    echo "警告: bc工具未安装，时间计算可能不准确"
    echo "安装命令: sudo apt-get install bc 或 sudo yum install bc"
fi

# 执行主函数
main "$@"
