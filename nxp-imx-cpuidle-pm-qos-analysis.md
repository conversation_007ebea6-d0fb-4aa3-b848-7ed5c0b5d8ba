# NXP i.MX平台cpuidle策略和PM_QOS_CPU_DMA_LATENCY分析

## 问题背景

在升级NXP i.MX8MP的WiFi驱动（AW-CM358）时发现，旧驱动调用`pm_qos_add_request(&pmhandle->woal_pm_qos_req, PM_QOS_CPU_DMA_LATENCY, 0)`将系统的PM_QOS_CPU_DMA_LATENCY设置为0，而新驱动没有此代码，导致系统PM_QOS_CPU_DMA_LATENCY变成默认值2000000000，引发了CAN总线占用率偏低和触摸抬起事件丢失等问题。

## PM_QOS_CPU_DMA_LATENCY默认值设置位置

### 1. 内核源码定义位置

PM_QOS_CPU_DMA_LATENCY的默认值在Linux内核中的定义位置：

```c
// 文件：include/linux/pm_qos.h
#define PM_QOS_CPU_DMA_LATENCY_DEFAULT_VALUE    2000000000  // 2秒，单位微秒

// 文件：kernel/power/qos.c
static struct pm_qos_constraints cpu_dma_constraints = {
    .list = PLIST_HEAD_INIT(cpu_dma_constraints.list),
    .target_value = PM_QOS_CPU_DMA_LATENCY_DEFAULT_VALUE,
    .default_value = PM_QOS_CPU_DMA_LATENCY_DEFAULT_VALUE,
    .no_constraint_value = PM_QOS_CPU_DMA_LATENCY_DEFAULT_VALUE,
    .type = PM_QOS_MIN,
    .notifiers = &cpu_dma_lat_notifier,
};
```

### 2. 查看系统当前默认值的方法

```bash
# 方法1：查看/dev/cpu_dma_latency设备节点
# 注意：只有在有进程打开此设备时才能读取
hexdump -C /dev/cpu_dma_latency 2>/dev/null || echo "No process using cpu_dma_latency"

# 方法2：通过debugfs查看PM QoS状态
cat /sys/kernel/debug/pm_qos/cpu_dma_latency/requests

# 方法3：查看cpuidle状态（间接方法）
cat /sys/devices/system/cpu/cpu0/cpuidle/state*/name
cat /sys/devices/system/cpu/cpu0/cpuidle/state*/usage

# 方法4：使用powertop工具
powertop --dump

# 方法5：检查内核日志中的PM QoS信息
dmesg | grep -i "pm_qos\|cpuidle"
```

### 3. 运行时检查脚本

```bash
#!/bin/bash
# check_pm_qos_status.sh - 检查PM QoS状态

echo "=== PM QoS CPU DMA Latency 状态检查 ==="

# 检查是否有进程在使用cpu_dma_latency
if [ -c /dev/cpu_dma_latency ]; then
    echo "1. /dev/cpu_dma_latency 设备节点存在"
    
    # 尝试读取当前值
    if timeout 1 hexdump -C /dev/cpu_dma_latency 2>/dev/null; then
        echo "   当前有进程在使用cpu_dma_latency"
    else
        echo "   当前没有进程在使用cpu_dma_latency（使用默认值）"
    fi
else
    echo "1. /dev/cpu_dma_latency 设备节点不存在"
fi

# 检查debugfs中的PM QoS信息
echo "2. PM QoS debugfs信息："
if [ -f /sys/kernel/debug/pm_qos/cpu_dma_latency/requests ]; then
    cat /sys/kernel/debug/pm_qos/cpu_dma_latency/requests
else
    echo "   debugfs不可用或未挂载"
fi

# 检查cpuidle状态
echo "3. CPUIdle状态："
for state in /sys/devices/system/cpu/cpu0/cpuidle/state*; do
    if [ -d "$state" ]; then
        name=$(cat $state/name 2>/dev/null)
        usage=$(cat $state/usage 2>/dev/null)
        time=$(cat $state/time 2>/dev/null)
        disable=$(cat $state/disable 2>/dev/null)
        echo "   $(basename $state): $name, usage=$usage, time=$time, disabled=$disable"
    fi
done

# 检查相关进程
echo "4. 可能影响PM QoS的进程："
ps aux | grep -E "(wifi|wlan|can|touch)" | grep -v grep
```

## 不同平台策略差异分析

### 1. i.MX8MP和i.MX6Q（PM_QOS_CPU_DMA_LATENCY = 0）

**设置原因：**
- **实时性要求**：这些平台通常用于工业控制、汽车电子等对实时性要求极高的场景
- **硬件限制**：早期的电源管理单元(PMU)和DMA控制器设计可能存在深度睡眠恢复的兼容性问题
- **中断响应**：某些关键外设（如CAN、触摸屏）需要极低的中断响应延迟
- **历史兼容性**：为了保持与旧版本BSP的兼容性

**技术细节：**
```bash
# 当PM_QOS_CPU_DMA_LATENCY = 0时，CPU只能进入最浅的睡眠状态
# 查看cpuidle状态
cat /sys/devices/system/cpu/cpu0/cpuidle/state*/name
# 输出通常只有：POLL, WFI

# 查看使用统计，深度状态使用次数为0
cat /sys/devices/system/cpu/cpu0/cpuidle/state*/usage
```

### 2. i.MX8MM（PM_QOS_CPU_DMA_LATENCY = 默认值）

**允许深度睡眠的原因：**
- **改进的硬件设计**：更先进的电源管理单元和时钟恢复机制
- **优化的DMA控制器**：支持更好的状态保存和恢复
- **应用场景差异**：主要用于消费电子，对功耗敏感度高于实时性要求
- **内核优化**：更新的内核版本对深度睡眠的支持更完善

**技术细节：**
```bash
# 当使用默认值时，CPU可以进入更深的睡眠状态
cat /sys/devices/system/cpu/cpu0/cpuidle/state*/name
# 输出可能包括：POLL, WFI, RETENTION, POWER_DOWN

# 深度状态也会被使用
cat /sys/devices/system/cpu/cpu0/cpuidle/state*/usage
```

## 问题影响分析

### 1. CAN总线占用率偏低

**原因分析：**
- CPU进入深度睡眠状态后，唤醒延迟增加（通常5-50ms）
- CAN中断处理延迟导致数据包丢失或处理不及时
- 深度睡眠状态下，某些时钟域可能被关闭，影响CAN控制器性能

**验证方法：**
```bash
# 监控CAN中断处理延迟
cat /proc/interrupts | grep can
watch -n 1 'cat /proc/interrupts | grep can'

# 检查CAN错误统计
ip -s link show can0
candump can0 -e  # 显示错误帧
```

### 2. 触摸抬起事件丢失

**原因分析：**
- 触摸屏中断在CPU深度睡眠时响应延迟
- 快速的触摸抬起动作可能在CPU唤醒前就结束
- 输入子系统的事件队列可能在深度睡眠期间溢出

**验证方法：**
```bash
# 监控触摸事件
evtest /dev/input/event0

# 检查输入设备中断
cat /proc/interrupts | grep -i input
cat /proc/interrupts | grep -i touch
```

## 解决方案

### 方案1：恢复旧驱动行为（最安全）

```c
// 在新的WiFi驱动中添加PM QoS请求
static struct pm_qos_request wifi_pm_qos_req;

// 在驱动初始化时添加
pm_qos_add_request(&wifi_pm_qos_req, PM_QOS_CPU_DMA_LATENCY, 0);

// 在驱动卸载时移除
pm_qos_remove_request(&wifi_pm_qos_req);
```

### 方案2：应用层控制

```bash
#!/bin/bash
# 创建PM QoS控制脚本

# 禁用深度睡眠（适用于实时性要求高的场景）
disable_deep_sleep() {
    echo 0 > /dev/cpu_dma_latency
    echo "Deep sleep disabled (PM_QOS_CPU_DMA_LATENCY = 0)"
}

# 允许浅度睡眠（平衡性能和功耗）
allow_shallow_sleep() {
    echo 50000 > /dev/cpu_dma_latency  # 50ms
    echo "Shallow sleep allowed (PM_QOS_CPU_DMA_LATENCY = 50ms)"
}

# 允许深度睡眠（最大功耗优化）
allow_deep_sleep() {
    # 关闭/dev/cpu_dma_latency文件描述符，恢复默认值
    exec 3>&-  # 如果之前打开了文件描述符
    echo "Deep sleep allowed (using default value)"
}

# 根据应用场景选择
case "$1" in
    "realtime")
        disable_deep_sleep
        ;;
    "balanced")
        allow_shallow_sleep
        ;;
    "powersave")
        allow_deep_sleep
        ;;
    *)
        echo "Usage: $0 {realtime|balanced|powersave}"
        exit 1
        ;;
esac
```

### 方案3：动态调整策略

```bash
#!/bin/bash
# dynamic_pm_qos.sh - 动态PM QoS管理

# 检查关键应用是否运行
check_critical_apps() {
    # 检查CAN应用
    if pgrep -f "can.*test\|socketcan" > /dev/null; then
        return 1  # 需要低延迟
    fi
    
    # 检查触摸应用
    if pgrep -f "touch.*app\|input.*test" > /dev/null; then
        return 1  # 需要低延迟
    fi
    
    # 检查实时应用
    if pgrep -f "rt.*app\|cyclictest" > /dev/null; then
        return 1  # 需要低延迟
    fi
    
    return 0  # 可以允许深度睡眠
}

# 动态调整PM QoS
adjust_pm_qos() {
    if check_critical_apps; then
        echo 10000 > /dev/cpu_dma_latency  # 10ms延迟限制
        echo "Critical apps detected, PM_QOS set to 10ms"
    else
        echo 100000 > /dev/cpu_dma_latency  # 100ms延迟限制
        echo "No critical apps, PM_QOS set to 100ms"
    fi
}

# 持续监控和调整
while true; do
    adjust_pm_qos
    sleep 5
done

### 方案4：针对性优化（推荐）

#### 4.1 CAN总线优化
```bash
# 针对CAN问题的专项优化
#!/bin/bash
# can_pm_optimization.sh

# 1. 提高CAN中断优先级
CAN_IRQ=$(cat /proc/interrupts | grep -i can | awk '{print $1}' | tr -d ':' | head -1)
if [ -n "$CAN_IRQ" ]; then
    echo "优化CAN中断 $CAN_IRQ"
    # 绑定CAN中断到CPU1（避开CPU0的系统中断）
    echo 2 > /proc/irq/$CAN_IRQ/smp_affinity
    echo "CAN中断已绑定到CPU1"
fi

# 2. 提高CAN相关进程优先级
for pid in $(pgrep -f "can\|socketcan"); do
    chrt -f -p 90 $pid 2>/dev/null && echo "CAN进程 $pid 优先级已提升"
done

# 3. 设置适中的延迟要求（允许浅度睡眠但保证CAN实时性）
echo 20000 > /dev/cpu_dma_latency  # 20ms延迟限制
echo "PM_QOS_CPU_DMA_LATENCY 设置为 20ms"

# 4. 优化CAN网络参数
echo 1000 > /proc/sys/net/core/netdev_max_backlog
echo "网络队列长度已优化"
```

#### 4.2 触摸屏优化
```bash
# 触摸屏事件处理优化
#!/bin/bash
# touch_pm_optimization.sh

# 1. 查找触摸屏设备和中断
TOUCH_DEVICE=$(find /dev/input -name "event*" -exec grep -l "touch\|Touch" /sys/class/input/{}/device/name \; 2>/dev/null | head -1)
if [ -n "$TOUCH_DEVICE" ]; then
    echo "找到触摸设备: $TOUCH_DEVICE"

    # 获取触摸中断号
    TOUCH_IRQ=$(cat /proc/interrupts | grep -i touch | awk '{print $1}' | tr -d ':' | head -1)
    if [ -n "$TOUCH_IRQ" ]; then
        echo "触摸中断号: $TOUCH_IRQ"
        # 绑定触摸中断到CPU2
        echo 4 > /proc/irq/$TOUCH_IRQ/smp_affinity
        echo "触摸中断已绑定到CPU2"
    fi
fi

# 2. 提高输入子系统相关进程优先级
for pid in $(pgrep -f "input\|evdev\|touch"); do
    chrt -f -p 85 $pid 2>/dev/null && echo "输入进程 $pid 优先级已提升"
done

# 3. 调整输入设备参数
for input_dev in /sys/class/input/input*; do
    if [ -f "$input_dev/device/poll_interval" ]; then
        echo 5 > "$input_dev/device/poll_interval"  # 5ms轮询间隔
    fi
done

# 4. 设置触摸优化的PM QoS
echo 15000 > /dev/cpu_dma_latency  # 15ms延迟限制
echo "PM_QOS_CPU_DMA_LATENCY 设置为 15ms（触摸优化）"
```

## 风险评估和缓解措施

### 主要风险

#### 1. 中断响应延迟增加
**风险等级：高**
- **影响**：实时性要求高的应用可能受影响
- **缓解措施**：
  - 使用中断亲和性绑定关键中断到特定CPU
  - 提高关键进程的实时优先级
  - 设置合理的PM_QOS_CPU_DMA_LATENCY值（10-50ms）

#### 2. DMA传输异常
**风险等级：中**
- **影响**：某些DMA传输可能在深度睡眠恢复后出现问题
- **缓解措施**：
  - 在关键DMA操作期间临时禁用深度睡眠
  - 实现DMA状态保存/恢复机制
  - 使用DMA一致性内存分配

#### 3. 外设状态丢失
**风险等级：中**
- **影响**：某些外设状态在深度睡眠后可能需要重新初始化
- **缓解措施**：
  - 实现外设状态保存/恢复机制
  - 在suspend/resume回调中正确处理外设状态
  - 使用wake-up源配置保持关键外设活跃

### 测试验证方案

```bash
#!/bin/bash
# comprehensive_test.sh - 全面测试脚本

# 测试CAN性能
test_can_performance() {
    echo "=== 测试CAN性能 ==="

    # 设置不同的PM QoS值并测试CAN吞吐量
    for latency in 0 10000 20000 50000 100000; do
        echo "测试 PM_QOS_CPU_DMA_LATENCY = $latency"
        echo $latency > /dev/cpu_dma_latency

        # 等待系统稳定
        sleep 2

        # 发送测试数据
        echo "发送CAN测试数据..."
        for i in {1..100}; do
            cansend can0 123#$(printf "%08X" $i) 2>/dev/null
        done

        # 统计接收情况
        timeout 5 candump can0 -n 100 > /tmp/can_test_$latency.log 2>&1 &
        CANDUMP_PID=$!
        sleep 3
        kill $CANDUMP_PID 2>/dev/null

        RECEIVED=$(wc -l < /tmp/can_test_$latency.log)
        echo "PM_QOS=$latency: 接收到 $RECEIVED/100 个数据包"

        # 检查错误统计
        ip -s link show can0 | grep -E "(RX|TX|errors)"
        echo "---"
    done
}

# 测试触摸响应
test_touch_response() {
    echo "=== 测试触摸响应 ==="

    TOUCH_DEVICE=$(find /dev/input -name "event*" -exec grep -l -i touch /sys/class/input/{}/device/name \; 2>/dev/null | head -1)

    if [ -z "$TOUCH_DEVICE" ]; then
        echo "未找到触摸设备"
        return
    fi

    echo "使用触摸设备: $TOUCH_DEVICE"

    for latency in 0 15000 50000 100000; do
        echo "测试 PM_QOS_CPU_DMA_LATENCY = $latency"
        echo $latency > /dev/cpu_dma_latency

        # 等待系统稳定
        sleep 2

        echo "请在10秒内进行触摸测试（按下和抬起）..."
        timeout 10 evtest $TOUCH_DEVICE > /tmp/touch_test_$latency.log 2>&1 &
        EVTEST_PID=$!

        sleep 10
        kill $EVTEST_PID 2>/dev/null

        # 分析触摸事件
        PRESS_EVENTS=$(grep -c "BTN_TOUCH.*1" /tmp/touch_test_$latency.log)
        RELEASE_EVENTS=$(grep -c "BTN_TOUCH.*0" /tmp/touch_test_$latency.log)

        echo "PM_QOS=$latency: 按下事件=$PRESS_EVENTS, 抬起事件=$RELEASE_EVENTS"

        if [ $PRESS_EVENTS -ne $RELEASE_EVENTS ]; then
            echo "警告: 按下和抬起事件不匹配！"
        fi
        echo "---"
    done
}

# 测试系统功耗
test_power_consumption() {
    echo "=== 测试系统功耗 ==="

    for latency in 0 50000 2000000000; do
        echo "测试 PM_QOS_CPU_DMA_LATENCY = $latency"
        echo $latency > /dev/cpu_dma_latency

        # 等待系统稳定
        sleep 5

        # 记录cpuidle统计
        echo "CPUIdle状态使用情况:"
        for state in /sys/devices/system/cpu/cpu0/cpuidle/state*; do
            if [ -d "$state" ]; then
                name=$(cat $state/name)
                usage_before=$(cat $state/usage)
                time_before=$(cat $state/time)
                echo "  $name: usage=$usage_before, time=$time_before"
            fi
        done

        # 等待一段时间让系统进入空闲状态
        echo "等待30秒测量空闲状态..."
        sleep 30

        # 再次记录统计
        echo "30秒后CPUIdle状态:"
        for state in /sys/devices/system/cpu/cpu0/cpuidle/state*; do
            if [ -d "$state" ]; then
                name=$(cat $state/name)
                usage_after=$(cat $state/usage)
                time_after=$(cat $state/time)
                echo "  $name: usage=$usage_after, time=$time_after"
            fi
        done
        echo "---"
    done
}

# 主测试函数
main() {
    echo "开始NXP i.MX平台PM QoS全面测试"
    echo "测试时间: $(date)"
    echo "平台信息: $(uname -a)"
    echo "=================================="

    test_can_performance
    test_touch_response
    test_power_consumption

    echo "测试完成，请查看 /tmp/can_test_*.log 和 /tmp/touch_test_*.log 文件"
}

# 运行测试
main

## 推荐实施策略

### 短期方案（立即实施）
1. **应用层控制**：使用脚本设置PM_QOS_CPU_DMA_LATENCY为适中值（20-50ms）
2. **中断优化**：绑定CAN和触摸中断到不同CPU核心
3. **进程优先级**：提高关键应用的实时优先级

### 中期方案（1-2周内实施）
1. **动态管理**：实现基于应用场景的动态PM QoS调整
2. **驱动修改**：在关键驱动中添加PM QoS请求
3. **系统调优**：优化内核参数和中断处理

### 长期方案（下个版本规划）
1. **BSP升级**：考虑升级到更新的BSP版本
2. **硬件优化**：评估硬件设计对深度睡眠的支持
3. **应用重构**：重新设计对实时性敏感的应用架构

## 监控和维护

### 1. 日常监控脚本
```bash
#!/bin/bash
# daily_pm_monitor.sh - 日常PM QoS监控

LOG_FILE="/var/log/pm_qos_monitor.log"

monitor_pm_qos() {
    echo "$(date): PM QoS监控开始" >> $LOG_FILE

    # 检查当前PM QoS状态
    if [ -f /sys/kernel/debug/pm_qos/cpu_dma_latency/requests ]; then
        echo "当前PM QoS请求:" >> $LOG_FILE
        cat /sys/kernel/debug/pm_qos/cpu_dma_latency/requests >> $LOG_FILE
    fi

    # 检查cpuidle使用情况
    echo "CPUIdle状态:" >> $LOG_FILE
    for cpu in /sys/devices/system/cpu/cpu*/cpuidle; do
        if [ -d "$cpu" ]; then
            echo "  CPU $(basename $(dirname $cpu)):" >> $LOG_FILE
            for state in $cpu/state*; do
                if [ -d "$state" ]; then
                    name=$(cat $state/name)
                    usage=$(cat $state/usage)
                    time=$(cat $state/time)
                    echo "    $name: usage=$usage, time=$time" >> $LOG_FILE
                fi
            done
        fi
    done

    # 检查关键应用状态
    echo "关键应用状态:" >> $LOG_FILE
    ps aux | grep -E "(can|touch|wifi)" | grep -v grep >> $LOG_FILE

    echo "$(date): PM QoS监控结束" >> $LOG_FILE
    echo "---" >> $LOG_FILE
}

# 每小时监控一次
while true; do
    monitor_pm_qos
    sleep 3600
done
```

### 2. 告警机制
```bash
#!/bin/bash
# pm_qos_alert.sh - PM QoS异常告警

check_can_errors() {
    CAN_ERRORS=$(ip -s link show can0 2>/dev/null | grep -o "errors [0-9]*" | awk '{print $2}')
    if [ -n "$CAN_ERRORS" ] && [ "$CAN_ERRORS" -gt 10 ]; then
        echo "警告: CAN错误数量过高 ($CAN_ERRORS)"
        return 1
    fi
    return 0
}

check_touch_responsiveness() {
    # 检查最近的触摸事件时间戳
    LAST_TOUCH=$(dmesg | grep -i touch | tail -1 | awk '{print $1}' | tr -d '[]')
    CURRENT_TIME=$(cat /proc/uptime | awk '{print $1}')

    if [ -n "$LAST_TOUCH" ]; then
        TIME_DIFF=$(echo "$CURRENT_TIME - $LAST_TOUCH" | bc 2>/dev/null)
        if [ -n "$TIME_DIFF" ] && [ $(echo "$TIME_DIFF > 300" | bc) -eq 1 ]; then
            echo "警告: 触摸设备可能无响应 (超过5分钟无事件)"
            return 1
        fi
    fi
    return 0
}

# 主检查函数
main_check() {
    ALERT_COUNT=0

    if ! check_can_errors; then
        ((ALERT_COUNT++))
    fi

    if ! check_touch_responsiveness; then
        ((ALERT_COUNT++))
    fi

    if [ $ALERT_COUNT -gt 0 ]; then
        echo "发现 $ALERT_COUNT 个问题，建议检查PM QoS配置"
        # 这里可以添加邮件通知或其他告警机制
    fi
}

# 定期检查
while true; do
    main_check
    sleep 300  # 每5分钟检查一次
done
```

## 总结

通过本文的分析，我们了解到：

1. **PM_QOS_CPU_DMA_LATENCY默认值为2000000000微秒（2秒）**，定义在内核源码的`include/linux/pm_qos.h`和`kernel/power/qos.c`中

2. **不同NXP平台采用不同策略的原因**：
   - i.MX8MP/i.MX6Q：优先保证实时性，设置为0禁用深度睡眠
   - i.MX8MM：平衡功耗和性能，使用默认值允许深度睡眠

3. **问题的根本原因**：CPU进入深度睡眠状态导致中断响应延迟增加，影响CAN和触摸等实时性敏感的应用

4. **推荐的解决方案**：
   - 短期：设置适中的PM_QOS值（20-50ms）
   - 中期：实现动态PM QoS管理
   - 长期：考虑BSP升级和硬件优化

5. **风险控制**：通过中断亲和性、进程优先级调整和监控告警机制来降低风险

选择合适的PM QoS策略需要在实时性和功耗之间找到平衡点，建议根据具体的应用场景和性能要求来制定相应的策略。
```
