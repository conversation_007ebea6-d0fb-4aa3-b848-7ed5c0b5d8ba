# 手把手教你基于i.MX93-EVK + MIPI-DSI屏幕定制U-Boot启动Logo

## 目录

1. [简介](#简介)
2. [硬件环境准备](#硬件环境准备)
3. [软件环境准备](#软件环境准备)
4. [U-Boot Logo机制解析](#uboot-logo机制解析)
5. [定制Logo图片](#定制logo图片)
6. [修改U-Boot配置](#修改uboot配置)
7. [MIPI-DSI屏幕配置](#mipi-dsi屏幕配置)
8. [编译和烧录](#编译和烧录)
9. [故障排除](#故障排除)
10. [进阶优化](#进阶优化)

## 简介

在嵌入式系统开发中，定制化的启动Logo不仅能提升产品的专业形象，还能在系统启动过程中为用户提供视觉反馈。本文将详细介绍如何在NXP i.MX93-EVK开发板上，配合MIPI-DSI屏幕实现自定义U-Boot启动Logo的完整流程。

### 适用场景
- 产品品牌定制化需求
- 嵌入式设备启动界面美化
- 系统启动状态指示
- 开发调试过程中的视觉确认

## 硬件环境准备

### 必需硬件
- **i.MX93-EVK开发板**：NXP官方评估板
- **MIPI-DSI显示屏**：推荐使用MX8-DSI-OLED1A显示屏
- **电源适配器**：12V/2A电源供应
- **microSD卡**：用于存储系统镜像（建议32GB以上）
- **USB Type-C线缆**：用于调试和烧录

### 硬件连接

```mermaid
graph LR
    A[i.MX93-EVK] --> B[MIPI-DSI接口]
    B --> C[MX8-DSI-OLED1A屏幕]
    A --> D[电源接口]
    A --> E[USB调试接口]

    style A fill:#e1f5fe
    style C fill:#f3e5f5
```

### 屏幕规格参数
- **分辨率**：1080 x 1920 (竖屏)
- **接口类型**：4-lane MIPI-DSI
- **色彩深度**：24-bit RGB
- **刷新率**：60Hz

## 软件环境准备

### 开发环境要求
- **操作系统**：Ubuntu 20.04 LTS 或更高版本
- **交叉编译工具链**：aarch64-linux-gnu-gcc
- **U-Boot源码**：NXP官方u-boot-imx仓库 (BSP L6.6.52_2.2.0)
- **图像处理工具**：netpbm工具包

### 环境搭建

```bash
# 安装必要的开发工具
sudo apt update
sudo apt install -y build-essential git flex bison libssl-dev
sudo apt install -y gcc-aarch64-linux-gnu
sudo apt install -y device-tree-compiler u-boot-tools

# 安装netpbm图像处理工具包
sudo apt install -y netpbm

# 获取U-Boot源码 (NXP BSP L6.6.52_2.2.0)
git clone https://github.com/nxp-imx/uboot-imx.git
cd uboot-imx
git checkout lf_v2024.04  # BSP L6.6.52_2.2.0对应的U-Boot版本

# 验证分支信息
git log --oneline -5
```

## U-Boot Logo机制解析

### Logo存储位置

U-Boot中的Logo图片存储在特定目录中：

```bash
# Logo图片存储目录
uboot-imx/tools/logos/

# 默认Logo文件
uboot-imx/tools/logos/denx.bmp  # NXP默认Logo
```

### Logo选择机制

在NXP BSP L6.6.52_2.2.0版本中，Logo的选择机制已经简化。实际上，U-Boot会直接使用`tools/logos/denx.bmp`作为默认Logo文件。

**最简单的方法**：直接替换默认Logo文件
```bash
# 直接替换默认Logo（推荐方法）
cp your_custom_logo.bmp uboot-imx/tools/logos/denx.bmp
```

**注意**：在L6.6.52_2.2.0版本中，`tools/Makefile`的Logo选择逻辑已经被简化，不再需要复杂的条件判断。

### Logo显示流程

```mermaid
flowchart LR
    A[U-Boot启动] --> B[初始化显示]
    B --> C[加载Logo数据]
    C --> D[解析BMP格式]
    D --> E[显示到屏幕]

    style C fill:#e2f0cb
    style E fill:#ffd2a5
```

## 定制Logo图片

### 图片格式要求

由于flash.bin大小限制，需要合理控制Logo图片的尺寸：

```bash
# 推荐的图片参数
分辨率: 480 x 854 (竖屏比例)
格式: 24-bit BMP
颜色深度: RGB888
文件大小: < 2MB
```

### 图片制作流程

使用netpbm工具链制作Logo的简化流程：

```bash
# 一步完成：转换格式 + 调整尺寸 + 生成BMP
anytopnm original_logo.png | pamscale -xyfit 480 854 | pnmpad -width 480 -height 854 -halign 0.5 -valign 0.5 -color white | ppmtobmp > custom_logo.bmp

# 如果文件过大，添加颜色压缩
anytopnm original_logo.png | pamscale -xyfit 480 854 | pnmpad -width 480 -height 854 -halign 0.5 -valign 0.5 -color white | pnmquant 256 | ppmtobmp > custom_logo.bmp

# 验证结果
file custom_logo.bmp
ls -lh custom_logo.bmp
```

### 图片放置

```bash
# 将定制Logo复制到U-Boot源码目录
cp custom_logo.bmp uboot-imx/tools/logos/

# 或者替换默认Logo
cp custom_logo.bmp uboot-imx/tools/logos/denx.bmp
```



## 修改U-Boot配置

### 1. 准备Logo文件

基于L6.6.52_2.2.0版本，最简单的方法是直接替换默认Logo：

```bash
# 方法1：直接替换默认Logo文件（推荐）
cp custom_logo.bmp uboot-imx/tools/logos/denx.bmp

# 方法2：添加新的Logo文件并修改引用
cp custom_logo.bmp uboot-imx/tools/logos/my_logo.bmp
# 然后在编译时通过环境变量指定
export LOGO_BMP=tools/logos/my_logo.bmp
```

### 2. 启用Logo显示功能

编辑对应的defconfig文件（BSP L6.6.52_2.2.0版本）：

```bash
# 编辑i.MX93 EVK配置文件
vim configs/imx93_11x11_evk_defconfig

# 添加或确认以下配置（基于L6.6.52_2.2.0 BSP）
CONFIG_VIDEO=y
CONFIG_DM_VIDEO=y
CONFIG_VIDEO_LOGO=y
CONFIG_VIDEO_BMP_LOGO=y
CONFIG_SPLASH_SCREEN=y
CONFIG_SPLASH_SCREEN_ALIGN=y
CONFIG_VIDEO_BMP_RLE8=y
CONFIG_BMP_16BPP=y
CONFIG_BMP_24BPP=y
CONFIG_BMP_32BPP=y

# L6.6.52_2.2.0版本特有的配置
CONFIG_IMX_MIPI_DSI=y
CONFIG_VIDEO_IMX_DCSS=y
```

### 3. 配置显示参数

Logo的显示位置通过U-Boot配置选项和环境变量控制：

```bash
# 在defconfig中添加Logo位置配置
CONFIG_SPLASH_SCREEN_ALIGN=y
CONFIG_VIDEO_LOGO=y
CONFIG_VIDEO_BMP_LOGO=y

# Logo显示位置通过环境变量控制
# 在U-Boot启动时设置环境变量
setenv splashpos m,m    # 居中显示 (middle, middle)
# 或者
setenv splashpos 300,500  # 指定具体坐标 (x=300, y=500)

# 保存环境变量
saveenv
```

### 4. Logo显示机制说明（L6.6.52_2.2.0版本）

在NXP BSP L6.6.52_2.2.0版本中，Logo显示的函数调用链：

```c
// Logo显示函数调用链
video_post_probe()           // 视频设备初始化完成后
  -> show_splash()           // 显示启动画面
    -> video_bmp_display()   // 显示BMP图像到framebuffer
```

**关键点**：
- Logo显示在视频驱动初始化完成后自动触发
- 无需手动编写调用代码
- Logo文件路径：`tools/logos/denx.bmp`
- 显示位置通过`splashpos`环境变量控制

### 5. 环境变量详细配置

```bash
# Logo位置配置选项详解
# splashpos格式: x,y 或 位置标识符

# 位置标识符:
# m = middle (居中)
# l = left (左对齐)
# r = right (右对齐)
# t = top (顶部对齐)
# b = bottom (底部对齐)

# 常用配置示例:
setenv splashpos m,m      # 水平垂直都居中
setenv splashpos l,t      # 左上角
setenv splashpos r,b      # 右下角
setenv splashpos m,t      # 水平居中，顶部对齐

# 精确坐标配置 (针对1080x1920屏幕):
setenv splashpos 300,533  # 居中显示480x854的Logo
# 计算公式: x = (1080-480)/2 = 300, y = (1920-854)/2 = 533

# 其他相关环境变量:
setenv bootdelay 3        # 启动延迟，给Logo显示时间
setenv silent 1           # 静默启动，避免文字覆盖Logo
```

### 6. 默认配置文件修改

如果要设置默认的Logo位置，可以在include/configs/imx93_evk.h中添加：

```c
// 在 include/configs/imx93_evk.h 中添加默认环境变量
#define CONFIG_EXTRA_ENV_SETTINGS \
    "splashpos=m,m\0" \
    "bootdelay=3\0" \
    "silent=1\0" \
    /* 其他环境变量... */
```

## MIPI-DSI屏幕配置

### 禁用ADV7535 HDMI桥接器

为了让MIPI-DSI屏幕正常工作，需要禁用ADV7535（基于L6.6.52_2.2.0 BSP）：

```bash
# 编辑设备树文件 (L6.6.52_2.2.0版本路径)
vim arch/arm/dts/imx93-11x11-evk.dts
```

```diff
# 在设备树中禁用ADV7535 (L6.6.52_2.2.0版本)
@@ -191,7 +191,7 @@
        adv7535: hdmi@3d {
                compatible = "adi,adv7535";
                reg = <0x3d>;
                adi,addr-cec = <0x3c>;
                adi,dsi-lanes = <4>;
-               status = "okay";
+               status = "disabled";
        };
```

### 配置MIPI-DSI显示参数

在L6.6.52_2.2.0版本的设备树中，默认已经包含了`rm67199_panel`节点定义，支持MX8-DSI-OLED1A屏幕。

**检查现有配置**：
```bash
# 查看设备树中的MIPI-DSI配置
grep -A 20 "rm67199_panel" arch/arm/dts/imx93-11x11-evk.dts

# 确认MIPI-DSI节点状态
grep -A 5 "&mipi_dsi" arch/arm/dts/imx93-11x11-evk.dts
```

**通常情况下无需修改**，默认配置已经适配MX8-DSI-OLED1A屏幕：
- 分辨率：1080x1920
- 4-lane MIPI-DSI接口
- 正确的时序参数

### 配置GPIO引脚

确保MIPI-DSI相关的GPIO配置正确：

```dts
&iomuxc {
    pinctrl_mipi_dsi_en: mipi_dsi_en {
        fsl,pins = <
            MX93_PAD_GPIO_IO16__GPIO2_IO16    0x31e
        >;
    };
};
```

## 编译和烧录

### 编译U-Boot

```bash
# 设置交叉编译环境 (L6.6.52_2.2.0 BSP)
export ARCH=arm64
export CROSS_COMPILE=aarch64-linux-gnu-

# 清理之前的编译
make distclean

# 配置编译目标 (L6.6.52_2.2.0版本)
make imx93_11x11_evk_defconfig

# 编译U-Boot (包含ATF和其他组件)
make -j$(nproc)

# L6.6.52_2.2.0版本编译完成后会生成以下文件
ls -la flash.bin u-boot.bin u-boot-spl.bin u-boot-nodtb.bin

# 验证flash.bin包含了所有必要组件
file flash.bin
```

### 验证BSP版本信息

```bash
# 检查U-Boot版本信息
strings u-boot.bin | grep -i "u-boot"
strings u-boot.bin | grep -i "2024"

# 检查是否包含i.MX93相关配置
grep -r "CONFIG_IMX93" .config
```

### 检查flash.bin大小

```bash
# 检查生成的flash.bin大小
ls -lh flash.bin

# 如果文件过大，需要优化Logo图片
# flash.bin大小限制通常在8MB以内
```

### 烧录到SD卡

```bash
# 假设SD卡设备为/dev/sdX (请根据实际情况修改)
sudo dd if=flash.bin of=/dev/sdX bs=1024 seek=32 conv=fsync

# 或者使用NXP的UUU工具烧录到eMMC
sudo uuu -b emmc_all flash.bin rootfs.img
```

## 测试验证

### 启动测试

```bash
# 插入SD卡到开发板
# 连接MIPI-DSI屏幕
# 上电启动

# 通过串口查看启动日志
sudo minicom -D /dev/ttyUSB0 -b 115200
```

### 预期效果

启动过程中应该看到：

```mermaid
sequenceDiagram
    participant Power as 上电
    participant UBoot as U-Boot
    participant Display as MIPI-DSI屏幕
    participant Logo as 自定义Logo

    Power->>UBoot: 系统启动
    UBoot->>Display: 初始化显示
    UBoot->>Logo: 加载Logo数据
    Logo->>Display: 显示自定义Logo
    Note over Display: Logo显示3-5秒
    UBoot->>Display: 进入Linux启动
```

## 故障排除

### 常见问题及解决方案

#### 1. Logo不显示

**可能原因：**
- MIPI-DSI屏幕未正确连接
- 设备树配置错误
- Logo文件格式不正确

**解决方案：**
```bash
# 检查设备树编译是否成功 (L6.6.52_2.2.0版本)
dtc -I dtb -O dts arch/arm/dts/imx93-11x11-evk.dtb | grep -A 20 mipi_dsi

# 使用netpbm验证Logo文件格式
file tools/logos/custom_logo.bmp
pnmfile tools/logos/custom_logo.bmp

# 检查图片是否为有效的BMP格式
bmptopnm tools/logos/custom_logo.bmp > /dev/null && echo "BMP格式正确" || echo "BMP格式错误"
```

#### 2. 屏幕显示异常

**可能原因：**
- 显示时序参数不匹配
- 电源供应不足
- 信号线连接问题

**解决方案：**
```bash
# 检查显示时序配置
# 确认屏幕规格参数与设备树配置一致

# 测试基本显示功能
# 在U-Boot命令行中执行
=> video list
=> video test
```

#### 3. flash.bin过大

**可能原因：**
- Logo图片文件过大
- 编译配置包含过多功能

**解决方案：**
```bash
# 使用netpbm压缩Logo图片
anytopnm original_logo.png | pamscale -width 320 -height 568 | pnmquant 128 | ppmtobmp > compressed_logo.bmp

# 或者使用更激进的压缩
anytopnm original_logo.png | pamscale -width 240 -height 426 | pnmquant 64 | ppmtobmp > small_logo.bmp

# 检查压缩后的文件大小
ls -lh tools/logos/*.bmp

# 或者禁用不必要的U-Boot功能 (L6.6.52_2.2.0版本)
# 在defconfig中移除非必需的CONFIG选项
```

#### 4. 编译错误

**常见编译错误及解决：**

```bash
# 错误：missing cross-compiler
sudo apt install gcc-aarch64-linux-gnu

# 错误：device tree compilation failed
sudo apt install device-tree-compiler

# 错误：missing tools
sudo apt install u-boot-tools flex bison
```

## 进阶配置

### 1. 多Logo支持

通过环境变量选择不同的Logo文件：

```bash
# 设置不同的Logo文件
setenv logo_file "logos/production_logo.bmp"
# 或
setenv logo_file "logos/debug_logo.bmp"

# 在编译时指定
export LOGO_BMP=$logo_file
make -j$(nproc)
```

### 2. Logo显示时间控制

```bash
# 控制Logo显示时间
setenv bootdelay 3        # U-Boot延迟3秒
setenv silent 1           # 静默启动，避免文字覆盖Logo
```

## 性能优化建议

### 1. 启动时间优化

```bash
# 减少U-Boot延迟
setenv bootdelay 1

# 优化Logo显示时间
setenv logo_display_time 2000  # 2秒
```

### 2. 内存使用优化

```c
// 使用压缩格式减少内存占用
#define CONFIG_VIDEO_BMP_RLE8
#define CONFIG_SYS_VIDEO_LOGO_MAX_SIZE (2 * 1024 * 1024)
```

### 3. 显示质量优化

```bash
# 使用netpbm进行高质量的图片转换
anytopnm input.png | pamscale -filter=lanczos -width 480 -height 854 | pnmgamma 1.0 | ppmtobmp > output.bmp

# 或者使用抗锯齿处理
anytopnm input.png | pamscale -filter=sinc -width 480 -height 854 | pnmsmooth | ppmtobmp > smooth_output.bmp
```

## 总结

通过本教程，我们详细介绍了在i.MX93-EVK开发板上定制U-Boot启动Logo的完整流程。主要包括：

### 关键要点

1. **硬件配置**：正确连接MIPI-DSI屏幕并禁用ADV7535
2. **图片处理**：制作符合尺寸和格式要求的Logo图片
3. **代码修改**：修改U-Boot源码和设备树配置
4. **编译烧录**：正确编译并烧录到目标设备

### 技术要点

- **尺寸限制**：flash.bin大小限制要求合理控制Logo文件大小
- **格式要求**：24-bit BMP格式确保最佳兼容性
- **显示配置**：正确的MIPI-DSI时序参数是显示成功的关键
- **设备树修改**：禁用冲突的显示设备确保MIPI-DSI正常工作

### 应用价值

定制化的启动Logo不仅提升了产品的专业形象，还为用户提供了良好的视觉体验。在实际产品开发中，这种定制化能力对于品牌建设和用户体验都具有重要意义。

通过掌握这些技术，开发者可以为自己的嵌入式产品创造独特的启动体验，提升产品的整体品质和用户满意度。
