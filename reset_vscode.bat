@echo off
:: Reset VS Code machine code by cleaning local settings and cache
::
:: This batch script removes VS Code machine-specific identifiers by deleting:
:: - %APPDATA%\Code (settings, extensions)
:: - %USERPROFILE%\.vscode (project settings)
:: Creates a backup before deletion.

:: Get timestamp for backup folder
for /f "tokens=1-3 delims=/ " %%a in ("%date%") do (
    set backup_date=%%c-%%b-%%a
)
set backup_time=%time::=-%
set backup_time=%backup_time: =0%
set backup_dir="%USERPROFILE%\vscode_backup_%backup_date%_%backup_time%"

:: VS Code directories to reset
set target1="%APPDATA%\Code"
set target2="%USERPROFILE%\.vscode"

echo VS Code Machine Code Reset Script
echo This will remove all VS Code settings and extensions.
echo A backup will be created at: %backup_dir%
echo.
echo WARNING: This cannot be undone!
echo.

choice /c yn /n /m "Are you sure you want to continue? (y/n)"
if errorlevel 2 goto :eof

:: Create backup directory
mkdir %backup_dir%

:: Backup and remove target directories
if exist %target1% (
    echo Backing up and removing: %target1%
    xcopy %target1% %backup_dir%\Code /e /i /h /k
    rd /s /q %target1%
)

if exist %target2% (
    echo Backing up and removing: %target2%
    xcopy %target2% %backup_dir%\.vscode /e /i /h /k
    rd /s /q %target2%
)

echo.
echo VS Code machine code has been reset.
echo Original settings backed up to: %backup_dir%
echo Restart VS Code for changes to take effect.
pause