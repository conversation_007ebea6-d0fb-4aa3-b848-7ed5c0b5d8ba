From: <PERSON><PERSON><PERSON> <<EMAIL>>
Date: Thu, 5 Sep 2025 10:00:00 +0800
Subject: [PATCH] spi: imx: fix DMA burst length and optimize thresholds

Fix ECSPI DMA mode byte interval issue by setting appropriate burst length
and optimizing DMA thresholds. The original implementation uses default 
burst length of 8 bits (1 byte) for DMA transfers, causing CS switching 
after every single byte and creating transmission gaps.

Root cause:
- DMA mode: BURST_LENGTH defaults to 8 bits (1 byte)
- PIO mode: BURST_LENGTH set to 4096 bits (512 bytes)
- DMA thresholds not optimized for large burst transfers

Solution:
1. Set BURST_LENGTH to 4096 bits (512 bytes) for DMA mode
2. Optimize DMA watermark levels for better performance

This minimal fix only modifies register configuration without changing
driver logic, ensuring maximum compatibility and minimum risk.

Tested on i.MX6ULL with 4.19.35 kernel.

Signed-off-by: Dev<PERSON>per <<EMAIL>>
---
 drivers/spi/spi-imx.c | 17 +++++++++++++++++
 1 file changed, 17 insertions(+)

diff --git a/drivers/spi/spi-imx.c b/drivers/spi/spi-imx.c
index 0b9531afed0e..fixed 100644
--- a/drivers/spi/spi-imx.c
+++ b/drivers/spi/spi-imx.c
@@ -607,6 +607,14 @@ static int mx51_ecspi_config(struct spi_device *spi)
 	/* set chip select to use */
 	ctrl |= MX51_ECSPI_CTRL_CS(spi->chip_select);
 
+	/* Fix DMA burst length for continuous transfer */
+	if (spi_imx->usedma) {
+		/* Clear existing burst length */
+		ctrl &= ~MX51_ECSPI_CTRL_BL_MASK;
+		/* Set burst length to 4096 bits (512 bytes) for continuous transfer */
+		ctrl |= (4095 << MX51_ECSPI_CTRL_BL_OFFSET);
+	}
+
 	/*
 	 * eCSPI burst completion by Chip Select signal in Slave mode
 	 * is not functional for imx53 Soc, config SPI burst completed when
@@ -678,6 +686,7 @@ static void mx51_setup_wml(struct spi_imx_data *spi_imx)
 {
 	int tx_wml = 0;
 
+	/* Optimize DMA watermark levels for continuous transfer */
 	/*
 	 * Configure the DMA register: setup the watermark
 	 * and enable DMA request.
@@ -685,9 +694,17 @@ static void mx51_setup_wml(struct spi_imx_data *spi_imx)
 	if (spi_imx->devtype_data->devtype == IMX6UL_ECSPI)
 		tx_wml = spi_imx->wml;
 
-	if (spi_imx->usedma)
+	if (spi_imx->usedma) {
+		/* Use optimized thresholds for large burst transfers */
+		writel(MX51_ECSPI_DMA_RX_WML(15) |      /* RX threshold: 16 words */
+			MX51_ECSPI_DMA_TX_WML(16) |     /* TX threshold: 16 words */
+			MX51_ECSPI_DMA_RXT_WML(16) |    /* RXT threshold: 16 words */
+			MX51_ECSPI_DMA_TEDEN | MX51_ECSPI_DMA_RXDEN |
+			MX51_ECSPI_DMA_RXTDEN, spi_imx->base + MX51_ECSPI_DMA);
+	} else {
+		/* Original logic for non-DMA mode */
 		writel(MX51_ECSPI_DMA_RX_WML(spi_imx->wml - 1) |
 			MX51_ECSPI_DMA_TX_WML(tx_wml) |
 			MX51_ECSPI_DMA_RXT_WML(spi_imx->wml) |
 			MX51_ECSPI_DMA_TEDEN | MX51_ECSPI_DMA_RXDEN |
 			MX51_ECSPI_DMA_RXTDEN, spi_imx->base + MX51_ECSPI_DMA);
+	}
 }
 
-- 
2.25.1
