# Linux Kernel 5.10.35 NAPI Budget修改指南

## 概述

在Linux kernel 5.10.35中修改NAPI budget需要修改以下几个关键文件：
1. DPAA2以太网驱动
2. Broadcom网卡驱动（如果使用）
3. 网络核心子系统参数

## 1. DPAA2驱动修改

### 1.1 修改dpaa2-eth驱动

**文件位置**: `drivers/net/ethernet/freescale/dpaa2/dpaa2-eth.c`

```c
// 在文件顶部添加模块参数
static int napi_budget = 128;  // 从默认64改为128
module_param(napi_budget, int, 0644);
MODULE_PARM_DESC(napi_budget, "NAPI budget for DPAA2 ethernet driver");

// 找到dpaa2_eth_poll函数，通常在文件中间位置
static int dpaa2_eth_poll(struct napi_struct *napi, int budget)
{
    struct dpaa2_eth_channel *ch;
    struct dpaa2_eth_priv *priv;
    int rx_cleaned = 0, txconf_cleaned = 0;
    struct dpaa2_eth_fq *fq, *next;
    int store_cleaned, work_done;
    struct list_head rx_list;
    int err;

    ch = container_of(napi, struct dpaa2_eth_channel, napi);
    ch->stats.frames = 0;

    INIT_LIST_HEAD(&rx_list);

    // 修改这里：使用自定义的budget而不是传入的budget
    work_done = min(budget, napi_budget);  // 添加这行
    
    // 原有的轮询逻辑...
    do {
        err = dpaa2_io_service_pull_channel(ch->dpio, ch->ch_id,
                                          ch->store);
        if (unlikely(err))
            break;

        /* Refill pool if appropriate */
        refill_pool(priv, ch, priv->bpool);

        store_cleaned = consume_frames(ch, &rx_list);
        if (store_cleaned <= 0)
            break;
        if (store_cleaned > 0) {
            rx_cleaned += store_cleaned;
            /* More work available */
            if (rx_cleaned >= work_done)  // 使用修改后的work_done
                break;
        }
    } while (store_cleaned > 0);

    // 其余代码保持不变...
}
```

### 1.2 修改NAPI初始化

在同一文件中找到NAPI初始化函数：

```c
// 找到add_ch_napi函数
static void add_ch_napi(struct dpaa2_eth_priv *priv)
{
    struct dpaa2_eth_channel *ch;
    int i;

    for (i = 0; i < priv->num_channels; i++) {
        ch = priv->channel[i];
        /* NAPI weight *MUST* be a multiple of DPAA2_ETH_STORE_SIZE */
        // 修改这里：使用自定义的weight
        netif_napi_add(priv->net_dev, &ch->napi, dpaa2_eth_poll,
                      napi_budget);  // 原来可能是NAPI_POLL_WEIGHT
    }
}
```

## 2. 修改网络核心参数

### 2.1 修改默认dev_weight

**文件位置**: `net/core/dev.c`

```c
// 找到dev_weight的定义，通常在文件顶部
int dev_weight_rx_bias __read_mostly = 1;
int dev_weight_tx_bias __read_mostly = 1;

// 修改默认的dev_weight值
int weight_p __read_mostly = 128;  // 从64改为128
EXPORT_SYMBOL(weight_p);

// 或者直接修改NAPI_POLL_WEIGHT宏定义
// 在include/linux/netdevice.h中：
#define NAPI_POLL_WEIGHT 128  // 从64改为128
```

### 2.2 修改netdev_budget

在`net/core/dev.c`中：

```c
// 找到netdev_budget的定义
int netdev_budget __read_mostly = 600;  // 从300改为600
EXPORT_SYMBOL(netdev_budget);

// 或者在sysctl表中修改默认值
static struct ctl_table net_core_table[] = {
    {
        .procname   = "netdev_budget",
        .data       = &netdev_budget,
        .maxlen     = sizeof(int),
        .mode       = 0644,
        .proc_handler   = proc_dointvec,
        .extra1     = &zero,
        .extra2     = &int_max,
    },
    // 可以在这里设置默认值
};
```

## 3. Broadcom驱动修改（如果使用）

### 3.1 修改bnxt_en驱动

**文件位置**: `drivers/net/ethernet/broadcom/bnxt/bnxt.c`

```c
// 添加模块参数
static int bnxt_napi_budget = 128;
module_param_named(napi_budget, bnxt_napi_budget, int, 0644);
MODULE_PARM_DESC(napi_budget, "NAPI budget for BNXT driver");

// 找到bnxt_poll函数
static int bnxt_poll(struct napi_struct *napi, int budget)
{
    struct bnxt_napi *bnapi = container_of(napi, struct bnxt_napi, napi);
    struct bnxt *bp = bnapi->bp;
    int work_done = 0;

    // 使用自定义budget
    int effective_budget = min(budget, bnxt_napi_budget);

    if (test_bit(BNXT_STATE_FW_FATAL_COND, &bp->state)) {
        napi_complete(napi);
        return 0;
    }
    while (1) {
        work_done += bnxt_poll_work(bp, bnapi, effective_budget - work_done);

        if (work_done >= effective_budget) {
            if (!budget)
                work_done = budget;
            break;
        }
        // 其余逻辑...
    }
    return work_done;
}
```

### 3.2 修改NAPI初始化

```c
// 在bnxt_init_napi函数中
static void bnxt_init_napi(struct bnxt *bp)
{
    int i;
    unsigned int cp_nr_rings = bp->cp_nr_rings;

    for (i = 0; i < cp_nr_rings; i++) {
        struct bnxt_napi *bnapi = bp->bnapi[i];
        int (*poll_fn)(struct napi_struct *, int) = bnxt_poll;

        if (bp->flags & BNXT_FLAG_CHIP_P5)
            poll_fn = bnxt_poll_p5;
        else if (i >= bp->rx_nr_rings)
            poll_fn = bnxt_poll_nitroa0;

        // 使用自定义budget
        netif_napi_add(bp->dev, &bnapi->napi, poll_fn, bnxt_napi_budget);
    }
}
```

## 4. 编译配置

### 4.1 修改Kconfig（可选）

**文件位置**: `drivers/net/ethernet/freescale/dpaa2/Kconfig`

```kconfig
config FSL_DPAA2_ETH
    tristate "Freescale DPAA2 Ethernet"
    depends on FSL_MC_BUS && FSL_MC_DPIO
    select PHYLINK
    select PCS_LYNX
    help
      This is the DPAA2 Ethernet driver supporting Freescale SoCs
      with DPAA2 (DataPath Acceleration Architecture v2).
      
      The driver supports enhanced NAPI budget configuration for
      improved network performance under high load conditions.
```

### 4.2 编译步骤

```bash
# 1. 配置内核
make menuconfig
# 启用: Device Drivers -> Network device support -> Ethernet driver support -> Freescale devices -> DPAA2

# 2. 编译内核
make -j$(nproc) Image modules

# 3. 安装模块
make modules_install

# 4. 更新内核镜像
cp arch/arm64/boot/Image /boot/Image-5.10.35-custom
```

## 5. 运行时验证

### 5.1 检查模块参数

```bash
# 检查DPAA2驱动参数
cat /sys/module/dpaa2_eth/parameters/napi_budget

# 检查Broadcom驱动参数（如果使用）
cat /sys/module/bnxt_en/parameters/napi_budget

# 检查系统NAPI参数
cat /proc/sys/net/core/dev_weight
cat /proc/sys/net/core/netdev_budget
```

### 5.2 动态调整（测试用）

```bash
# 重新加载模块并设置参数
rmmod dpaa2-eth
modprobe dpaa2-eth napi_budget=128

# 或者在启动时设置
echo "options dpaa2-eth napi_budget=128" > /etc/modprobe.d/dpaa2-eth.conf
```

## 6. 性能测试脚本

```bash
#!/bin/bash
# napi_performance_test.sh

echo "=== NAPI Budget性能测试 ==="

# 测试前状态
echo "测试前统计:"
ethtool -S eth3 | grep -E "(rx_nobuffer|rx_packets|tx_packets)"

# 运行网络负载测试
echo "开始网络负载测试..."
iperf3 -s &
SERVER_PID=$!
sleep 2

# 客户端测试
iperf3 -c localhost -t 300 -P 4 &
CLIENT_PID=$!

# 监控中断和统计
for i in {1..60}; do
    echo "=== 第${i}分钟 ==="
    cat /proc/interrupts | grep -E "(eth3|dpaa2)" | head -3
    ethtool -S eth3 | grep rx_nobuffer
    sleep 60
done

# 清理
kill $CLIENT_PID $SERVER_PID
echo "测试完成"
```

## 7. 注意事项

### 7.1 兼容性考虑
- 确保修改不会影响其他网络功能
- 测试不同负载条件下的性能
- 验证功耗和延迟影响

### 7.2 调试选项
```c
// 在驱动中添加调试信息
#ifdef DEBUG_NAPI_BUDGET
    pr_debug("DPAA2: Using NAPI budget %d\n", napi_budget);
#endif
```

### 7.3 回滚方案
保留原始内核镜像以便出现问题时回滚：
```bash
cp /boot/Image /boot/Image-original-backup
```

这些修改将在内核级别实现NAPI budget的优化，从而减少MSI中断频率并提高网络性能。
