--- a/drivers/spi/spi-imx.c.orig
+++ b/drivers/spi/spi-imx.c
@@ -XXX,XX +XXX,XX @@ static int spi_imx_dma_transfer(struct spi_imx_data *spi_imx,
                                 struct spi_transfer *transfer)
 {
     struct dma_async_tx_descriptor *desc_tx, *desc_rx;
     unsigned long transfer_timeout, timeout;
     struct spi_master *master = spi_imx->bitbang.master;
     struct sg_table *tx = &transfer->tx_sg, *rx = &transfer->rx_sg;
+    u32 ctrl;
+    unsigned int burst_length_bits;
+
+    /* Fix burst length for large transfers */
+    if (transfer->len >= 512) {
+        burst_length_bits = 4096;  /* 512 bytes = 4096 bits */
+    } else if (transfer->len >= 64) {
+        burst_length_bits = transfer->len * 8;
+    } else {
+        burst_length_bits = 512;   /* minimum 64 bytes */
+    }
+    burst_length_bits = min(burst_length_bits, 4096U);
+
+    /* Configure burst length and ensure SMC=1 for DMA */
+    ctrl = readl(spi_imx->base + MXC_CSPICTRL);
+    ctrl &= ~MXC_CSPICTRL_BL_MASK;
+    ctrl |= (burst_length_bits - 1) << MXC_CSPICTRL_BL_OFFSET;
+    ctrl |= MXC_CSPICTRL_SMC;  /* Keep SMC=1 for DMA auto-start */
+    writel(ctrl, spi_imx->base + MXC_CSPICTRL);
+
+    /* Optimize DMA thresholds for large transfers */
+    if (transfer->len >= 256) {
+        u32 dma_reg = readl(spi_imx->base + MXC_CSPIDMAREG);
+        dma_reg &= ~(0x3F << 16);  /* Clear RX_THRESHOLD */
+        dma_reg &= ~0x3F;          /* Clear TX_THRESHOLD */
+        dma_reg |= (16 << 16);     /* RX_THRESHOLD = 16 */
+        dma_reg |= 16;             /* TX_THRESHOLD = 16 */
+        dma_reg |= (1 << 7);       /* TEDEN */
+        dma_reg |= (1 << 23);      /* RXDEN */
+        writel(dma_reg, spi_imx->base + MXC_CSPIDMAREG);
+    }
 
     /*
      * The TX DMA setup starts the transfer, so make sure RX is configured
@@ -XXX,XX +XXX,XX @@ static int spi_imx_dma_transfer(struct spi_imx_data *spi_imx,
     dma_async_issue_pending(spi_imx->dma_tx);
     dma_async_issue_pending(spi_imx->dma_rx);
 
-    /* Wait SDMA to finish the data transfer.*/
+    /* 
+     * SMC=1 mode: transfer starts automatically when FIFO has data
+     * No need to manually set XCH bit - this prevents DMA timeout!
+     */
     transfer_timeout = spi_imx_calculate_timeout(spi_imx, transfer->len);
 
     /* Wait for the completion of the RXFIFO */
