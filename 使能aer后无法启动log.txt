Starting kernel ...

[    0.000000] Booting Linux on physical CPU 0x0000000000 [0x412fd050]
[    0.000000] Linux version 6.12.3-lts-next-g37d02f4dcbbe-dirty (oe-user@oe-host) (aarch64-poky-linux-gcc (GCC) 14.2.0, GNU ld (GNU Binutils) 2.43.1) #1 SMP PREEMPT Sat Mar  1 12:54:50 UTC 2025
[    0.000000] KASLR disabled due to lack of seed
[    0.000000] Machine model: NXP i.MX95 19X19 board
[    0.000000] efi: UEFI not found.
[    0.000000] Reserved memory: created CMA memory pool at 0x00000000c3000000, size 960 MiB
[    0.000000] OF: reserved mem: initialized node linux,cma, compatible id shared-dma-pool
[    0.000000] OF: reserved mem: 0x00000000c3000000..0x00000000feffffff (983040 KiB) map reusable linux,cma
[    0.000000] OF: reserved mem: 0x0000000088000000..0x0000000088007fff (32 KiB) nomap non-reusable vdev0vring0@88000000
[    0.000000] OF: reserved mem: 0x0000000088008000..0x000000008800ffff (32 KiB) nomap non-reusable vdev0vring1@88008000
[    0.000000] OF: reserved mem: 0x0000000088010000..0x0000000088017fff (32 KiB) nomap non-reusable vdev1vring0@88010000
[    0.000000] OF: reserved mem: 0x0000000088018000..0x000000008801ffff (32 KiB) nomap non-reusable vdev1vring1@88018000
[    0.000000] Reserved memory: created DMA memory pool at 0x0000000088020000, size 1 MiB
[    0.000000] OF: reserved mem: initialized node vdevbuffer@88020000, compatible id shared-dma-pool
[    0.000000] OF: reserved mem: 0x0000000088020000..0x000000008811ffff (1024 KiB) nomap non-reusable vdevbuffer@88020000
[    0.000000] OF: reserved mem: 0x0000000088220000..0x0000000088220fff (4 KiB) nomap non-reusable rsc-table@88220000
[    0.000000] OF: reserved mem: 0x00000000a0000000..0x00000000a00fffff (1024 KiB) nomap non-reusable vpu_boot@a0000000
[    0.000000] earlycon: lpuart32 at MMIO32 0x0000000044380000 (options '')
[    0.000000] printk: legacy bootconsole [lpuart32] enabled
[    0.000000] NUMA: Faking a node at [mem 0x0000000090000000-0x000000047fffffff]
[    0.000000] NODE_DATA(0) allocated [mem 0x47e002680-0x47e0050bf]
[    0.000000] Zone ranges:
[    0.000000]   DMA      [mem 0x0000000090000000-0x00000000ffffffff]
[    0.000000]   DMA32    empty
[    0.000000]   Normal   [mem 0x0000000100000000-0x000000047fffffff]
[    0.000000] Movable zone start for each node
[    0.000000] Early memory node ranges
[    0.000000]   node   0: [mem 0x0000000090000000-0x000000009fffffff]
[    0.000000]   node   0: [mem 0x00000000a0000000-0x00000000a00fffff]
[    0.000000]   node   0: [mem 0x00000000a0100000-0x000000047fffffff]
[    0.000000] Initmem setup node 0 [mem 0x0000000090000000-0x000000047fffffff]
[    0.000000] psci: probing for conduit method from DT.
[    0.000000] psci: PSCIv1.1 detected in firmware.
[    0.000000] psci: Using standard PSCI v0.2 function IDs
[    0.000000] psci: MIGRATE_INFO_TYPE not supported.
[    0.000000] psci: SMC Calling Convention v1.4
[    0.000000] percpu: Embedded 32 pages/cpu s92760 r8192 d30120 u131072
[    0.000000] Detected VIPT I-cache on CPU0
[    0.000000] CPU features: detected: GIC system register CPU interface
[    0.000000] CPU features: detected: Virtualization Host Extensions
[    0.000000] CPU features: detected: ARM errata 1165522, 1319367, or 1530923
[    0.000000] alternatives: applying boot alternatives
[    0.000000] Kernel command line: console=ttyLP0,115200 earlycon root=/dev/mmcblk0p2 rootwait rw
[    0.000000] Dentry cache hash table entries: 2097152 (order: 12, 16777216 bytes, linear)
[    0.000000] Inode-cache hash table entries: 1048576 (order: 11, 8388608 bytes, linear)
[    0.000000] Fallback order for Node 0: 0
[    0.000000] Built 1 zonelists, mobility grouping on.  Total pages: 4128768
[    0.000000] Policy zone: Normal
[    0.000000] mem auto-init: stack:all(zero), heap alloc:off, heap free:off
[    0.000000] software IO TLB: area num 8.
[    0.000000] software IO TLB: mapped [mem 0x00000000bf000000-0x00000000c3000000] (64MB)
[    0.000000] SLUB: HWalign=64, Order=0-3, MinObjects=0, CPUs=6, Nodes=1
[    0.000000] ftrace: allocating 79677 entries in 312 pages
[    0.000000] ftrace: allocated 312 pages with 4 groups
[    0.000000]
[    0.000000] **********************************************************
[    0.000000] **   NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE   **
[    0.000000] **                                                      **
[    0.000000] ** trace_printk() being used. Allocating extra memory.  **
[    0.000000] **                                                      **
[    0.000000] ** This means that this is a DEBUG kernel and it is     **
[    0.000000] ** unsafe for production use.                           **
[    0.000000] **                                                      **
[    0.000000] ** If you see this message and you are not debugging    **
[    0.000000] ** the kernel, report this immediately to your vendor!  **
[    0.000000] **                                                      **
[    0.000000] **   NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE   **
[    0.000000] **********************************************************
[    0.000000] trace event string verifier disabled
[    0.000000] rcu: Preemptible hierarchical RCU implementation.
[    0.000000] rcu:     RCU event tracing is enabled.
[    0.000000] rcu:     RCU restricting CPUs from NR_CPUS=512 to nr_cpu_ids=6.
[    0.000000]  Trampoline variant of Tasks RCU enabled.
[    0.000000]  Rude variant of Tasks RCU enabled.
[    0.000000]  Tracing variant of Tasks RCU enabled.
[    0.000000] rcu: RCU calculated value of scheduler-enlistment delay is 25 jiffies.
[    0.000000] rcu: Adjusting geometry for rcu_fanout_leaf=16, nr_cpu_ids=6
[    0.000000] RCU Tasks: Setting shift to 3 and lim to 1 rcu_task_cb_adjust=1 rcu_task_cpu_ids=6.
[    0.000000] RCU Tasks Rude: Setting shift to 3 and lim to 1 rcu_task_cb_adjust=1 rcu_task_cpu_ids=6.
[    0.000000] RCU Tasks Trace: Setting shift to 3 and lim to 1 rcu_task_cb_adjust=1 rcu_task_cpu_ids=6.
[    0.000000] NR_IRQS: 64, nr_irqs: 64, preallocated irqs: 0
[    0.000000] GIC: enabling workaround for GICv3: non-coherent attribute
[    0.000000] GICv3: GIC: Using split EOI/Deactivate mode
[    0.000000] GIC: enabling workaround for GICv3: ARM64 erratum 2941627
[    0.000000] GICv3: 384 SPIs implemented
[    0.000000] GICv3: 0 Extended SPIs implemented
[    0.000000] Root IRQ handler: gic_handle_irq
[    0.000000] GICv3: GICv3 features: 16 PPIs, DirectLPI
[    0.000000] GICv3: GICD_CTRL.DS=0, SCR_EL3.FIQ=0
[    0.000000] GICv3: CPU0: found redistributor 0 region 0:0x0000000048060000
[    0.000000] ITS [mem 0x48040000-0x4805ffff]
[    0.000000] GIC: enabling workaround for ITS: non-coherent attribute
[    0.000000] ITS@0x0000000048040000: allocated 8192 Devices @1003d0000 (indirect, esz 8, psz 64K, shr 0)
[    0.000000] ITS@0x0000000048040000: allocated 32768 Interrupt Collections @1003e0000 (flat, esz 2, psz 64K, shr 0)
[    0.000000] ITS: using cache flushing for cmd queue
[    0.000000] GICv3: using LPI property table @0x00000001003f0000
[    0.000000] GIC: using cache flushing for LPI property table
[    0.000000] GICv3: CPU0: using allocated LPI pending table @0x0000000100400000
[    0.000000] rcu: srcu_init: Setting srcu_struct sizes based on contention.
[    0.000000] arch_timer: cp15 timer(s) running at 24.00MHz (phys).
[    0.000000] clocksource: arch_sys_counter: mask: 0xffffffffffffff max_cycles: 0x588fe9dc0, max_idle_ns: 440795202592 ns
[    0.000000] sched_clock: 56 bits at 24MHz, resolution 41ns, wraps every 4398046511097ns
[    0.008451] Console: colour dummy device 80x25
[    0.012706] Calibrating delay loop (skipped), value calculated using timer frequency.. 48.00 BogoMIPS (lpj=96000)
[    0.022871] pid_max: default: 32768 minimum: 301
[    0.027501] LSM: initializing lsm=capability
[    0.031815] Mount-cache hash table entries: 32768 (order: 6, 262144 bytes, linear)
[    0.039268] Mountpoint-cache hash table entries: 32768 (order: 6, 262144 bytes, linear)
[    0.051286] rcu: Hierarchical SRCU implementation.
[    0.055836] rcu:     Max phase no-delay instances is 1000.
[    0.061170] Timer migration: 1 hierarchy levels; 8 children per group; 1 crossnode level
[    0.069320] fsl-mc MSI: msi-controller@48040000 domain created
[    0.075333] EFI services will not be available.
[    0.083646] smp: Bringing up secondary CPUs ...
[    0.092629] Detected VIPT I-cache on CPU1
[    0.092707] GICv3: CPU1: found redistributor 100 region 0:0x0000000048080000
[    0.092718] GICv3: CPU1: using allocated LPI pending table @0x0000000100410000
[    0.092755] CPU1: Booted secondary processor 0x0000000100 [0x412fd050]
[    0.100727] Detected VIPT I-cache on CPU2
[    0.100804] GICv3: CPU2: found redistributor 200 region 0:0x00000000480a0000
[    0.100815] GICv3: CPU2: using allocated LPI pending table @0x0000000100420000
[    0.100848] CPU2: Booted secondary processor 0x0000000200 [0x412fd050]
[    0.101857] Detected VIPT I-cache on CPU3
[    0.101919] GICv3: CPU3: found redistributor 300 region 0:0x00000000480c0000
[    0.101928] GICv3: CPU3: using allocated LPI pending table @0x0000000100430000
[    0.101955] CPU3: Booted secondary processor 0x0000000300 [0x412fd050]
[    0.102844] Detected VIPT I-cache on CPU4
[    0.102899] GICv3: CPU4: found redistributor 400 region 0:0x00000000480e0000
[    0.102908] GICv3: CPU4: using allocated LPI pending table @0x0000000100440000
[    0.102932] CPU4: Booted secondary processor 0x0000000400 [0x412fd050]
[    0.108845] Detected VIPT I-cache on CPU5
[    0.108908] GICv3: CPU5: found redistributor 500 region 0:0x0000000048100000
[    0.108918] GICv3: CPU5: using allocated LPI pending table @0x0000000100450000
[    0.108943] CPU5: Booted secondary processor 0x0000000500 [0x412fd050]
[    0.109059] smp: Brought up 1 node, 6 CPUs
[    0.236417] SMP: Total of 6 processors activated.
[    0.241086] CPU: All CPU(s) started at EL2
[    0.245171] CPU features: detected: 32-bit EL0 Support
[    0.250271] CPU features: detected: Data cache clean to the PoU not required for I/D coherence
[    0.258862] CPU features: detected: Common not Private translations
[    0.265093] CPU features: detected: CRC32 instructions
[    0.270203] CPU features: detected: RCpc load-acquire (LDAPR)
[    0.275923] CPU features: detected: LSE atomic instructions
[    0.281468] CPU features: detected: Privileged Access Never
[    0.287014] CPU features: detected: RAS Extension Support
[    0.292391] CPU features: detected: Speculative Store Bypassing Safe (SSBS)
[    0.299358] alternatives: applying system-wide alternatives
[    0.307967] Memory: 15083208K/16515072K available (25152K kernel code, 3122K rwdata, 9420K rodata, 2816K init, 792K bss, 425076K reserved, 983040K cma-reserved)
[    0.325115] devtmpfs: initialized
[    0.336021] clocksource: jiffies: mask: 0xffffffff max_cycles: 0xffffffff, max_idle_ns: 7645041785100000 ns
[    0.345521] futex hash table entries: 2048 (order: 5, 131072 bytes, linear)
[    0.366553] 22400 pages in range for non-PLT usage
[    0.366568] 513920 pages in range for PLT usage
[    0.371291] pinctrl core: initialized pinctrl subsystem
[    0.382602] DMI not present or invalid.
[    0.387614] NET: Registered PF_NETLINK/PF_ROUTE protocol family
[    0.394167] DMA: preallocated 2048 KiB GFP_KERNEL pool for atomic allocations
[    0.401555] DMA: preallocated 2048 KiB GFP_KERNEL|GFP_DMA pool for atomic allocations
[    0.409652] DMA: preallocated 2048 KiB GFP_KERNEL|GFP_DMA32 pool for atomic allocations
[    0.417454] audit: initializing netlink subsys (disabled)
[    0.422946] audit: type=2000 audit(0.264:1): state=initialized audit_enabled=0 res=1
[    0.423327] thermal_sys: Registered thermal governor 'step_wise'
[    0.430492] thermal_sys: Registered thermal governor 'power_allocator'
[    0.436500] cpuidle: using governor menu
[    0.447051] hw-breakpoint: found 6 breakpoint and 4 watchpoint registers.
[    0.453732] ASID allocator initialised with 65536 entries
[    0.459239] Serial: AMBA PL011 UART driver
[    0.463124] imx mu driver is registered.
[    0.466982] imx rpmsg driver is registered.
[    0.483744] platform 4acf0000.dsi: Fixed dependency cycle(s) with /soc/bus@44000000/i2c@44350000/hdmi@3d
[    0.492995] platform 4acf0000.dsi: Fixed dependency cycle(s) with /soc/syscon@4b010000/bridge@8
[    0.502178] platform 4ad30000.csi: Fixed dependency cycle(s) with /soc/syscon@4ac10000/formatter@20
[    0.510986] platform 4ad30000.csi: Fixed dependency cycle(s) with /soc/bus@42000000/i2c@42530000/ap1302_mipi@3c
[    0.521222] platform 4ad50000.isi: Fixed dependency cycle(s) with /soc/syscon@4ac10000/formatter@20
[    0.530595] platform 4b400000.display-controller: Fixed dependency cycle(s) with /soc/bridge@4b0d0000/channel@0
[    0.540576] platform 4c1f0040.phy: Fixed dependency cycle(s) with /soc/bus@42000000/i2c@426d0000/tcpc@52/connector
[    0.556340] HugeTLB: registered 1.00 GiB page size, pre-allocated 0 pages
[    0.562889] HugeTLB: 0 KiB vmemmap can be freed for a 1.00 GiB page
[    0.569103] HugeTLB: registered 32.0 MiB page size, pre-allocated 0 pages
[    0.575861] HugeTLB: 0 KiB vmemmap can be freed for a 32.0 MiB page
[    0.582108] HugeTLB: registered 2.00 MiB page size, pre-allocated 0 pages
[    0.588866] HugeTLB: 0 KiB vmemmap can be freed for a 2.00 MiB page
[    0.595101] HugeTLB: registered 64.0 KiB page size, pre-allocated 0 pages
[    0.601862] HugeTLB: 0 KiB vmemmap can be freed for a 64.0 KiB page
[    0.609842] ACPI: Interpreter disabled.
[    0.614414] iommu: Default domain type: Translated
[    0.618969] iommu: DMA domain TLB invalidation policy: strict mode
[    0.633415] SCSI subsystem initialized
[    0.637195] usbcore: registered new interface driver usbfs
[    0.642441] usbcore: registered new interface driver hub
[    0.647725] usbcore: registered new device driver usb
[    0.653534] mc: Linux media interface: v0.10
[    0.657580] videodev: Linux video capture interface: v2.00
[    0.663043] pps_core: LinuxPPS API ver. 1 registered
[    0.667943] pps_core: Software ver. 5.3.6 - Copyright 2005-2007 Rodolfo Giometti <<EMAIL>>
[    0.677050] PTP clock support registered
[    0.680956] EDAC MC: Ver: 3.0.0
[    0.684298] scmi_core: SCMI protocol bus registered
[    0.689244] FPGA manager framework
[    0.692434] Advanced Linux Sound Architecture Driver Initialized.
[    0.698974] Bluetooth: Core ver 2.22
[    0.702294] NET: Registered PF_BLUETOOTH protocol family
[    0.707579] Bluetooth: HCI device and connection manager initialized
[    0.713906] Bluetooth: HCI socket layer initialized
[    0.718756] Bluetooth: L2CAP socket layer initialized
[    0.723787] Bluetooth: SCO socket layer initialized
[    0.728976] vgaarb: loaded
[    0.731879] clocksource: Switched to clocksource arch_sys_counter
[    0.737978] VFS: Disk quotas dquot_6.6.0
[    0.741666] VFS: Dquot-cache hash table entries: 512 (order 0, 4096 bytes)
[    0.748673] pnp: PnP ACPI: disabled
[    0.757447] NET: Registered PF_INET protocol family
[    0.762299] IP idents hash table entries: 262144 (order: 9, 2097152 bytes, linear)
[    0.776082] tcp_listen_portaddr_hash hash table entries: 8192 (order: 5, 131072 bytes, linear)
[    0.784539] Table-perturb hash table entries: 65536 (order: 6, 262144 bytes, linear)
[    0.792170] TCP established hash table entries: 131072 (order: 8, 1048576 bytes, linear)
[    0.800669] TCP bind hash table entries: 65536 (order: 9, 2097152 bytes, linear)
[    0.808986] TCP: Hash tables configured (established 131072 bind 65536)
[    0.815452] UDP hash table entries: 8192 (order: 6, 262144 bytes, linear)
[    0.822245] UDP-Lite hash table entries: 8192 (order: 6, 262144 bytes, linear)
[    0.829542] NET: Registered PF_UNIX/PF_LOCAL protocol family
[    0.835289] RPC: Registered named UNIX socket transport module.
[    0.840952] RPC: Registered udp transport module.
[    0.845624] RPC: Registered tcp transport module.
[    0.850299] RPC: Registered tcp-with-tls transport module.
[    0.855762] RPC: Registered tcp NFSv4.1 backchannel transport module.
[    0.862918] PCI: CLS 0 bytes, default 64
[    0.867676] Initialise system trusted keyrings
[    0.872046] workingset: timestamp_bits=42 max_order=22 bucket_order=0
[    0.878487] squashfs: version 4.0 (2009/01/31) Phillip Lougher
[    0.884292] NFS: Registering the id_resolver key type
[    0.889126] Key type id_resolver registered
[    0.893264] Key type id_legacy registered
[    0.897267] nfs4filelayout_init: NFSv4 File Layout Driver Registering...
[    0.903923] nfs4flexfilelayout_init: NFSv4 Flexfile Layout Driver Registering...
[    0.911365] jffs2: version 2.2. (NAND) © 2001-2006 Red Hat, Inc.
[    0.917527] 9p: Installing v9fs 9p2000 file system support
[    0.959417] Key type asymmetric registered
[    0.963249] Asymmetric key parser 'x509' registered
[    0.968138] Block layer SCSI generic (bsg) driver version 0.4 loaded (major 243)
[    0.975471] io scheduler mq-deadline registered
[    0.979984] io scheduler kyber registered
[    0.983981] io scheduler bfq registered
[    0.995487] ledtrig-cpu: registered to indicate activity on CPUs
[    1.006826] Bus freq driver module loaded
[    1.015681] Serial: 8250/16550 driver, 4 ports, IRQ sharing enabled
[    1.024474] arm-smmu-v3 490d0000.iommu: ias 40-bit, oas 36-bit (features 0x0074deaf)
[    1.032683] arm-smmu-v3 490d0000.iommu: allocated 65536 entries for cmdq
[    1.039770] arm-smmu-v3 490d0000.iommu: allocated 32768 entries for evtq
[    1.046230] arm-smmu-v3 490d0000.iommu: 2-level strtab only covers 25/32 bits of SID
[    1.054848] arm-smmu-v3 490d0000.iommu: msi_domain absent - falling back to wired irqs
[    1.073665] loop: module loaded
[    1.077882] megasas: 07.727.03.00-rc1
[    1.086505] tun: Universal TUN/TAP device driver, 1.6
[    1.091985] thunder_xcv, ver 1.0
[    1.094986] thunder_bgx, ver 1.0
[    1.098195] nicpf, ver 1.0
[    1.102609] hns3: Hisilicon Ethernet Network Driver for Hip08 Family - version
[    1.109603] hns3: Copyright (c) 2017 Huawei Corporation.
[    1.114963] hclge is initializing
[    1.118191] e1000: Intel(R) PRO/1000 Network Driver
[    1.123009] e1000: Copyright (c) 1999-2006 Intel Corporation.
[    1.128744] e1000e: Intel(R) PRO/1000 Network Driver
[    1.133666] e1000e: Copyright(c) 1999 - 2015 Intel Corporation.
[    1.139577] igb: Intel(R) Gigabit Ethernet Network Driver
[    1.144926] igb: Copyright (c) 2007-2014 Intel Corporation.
[    1.150491] igbvf: Intel(R) Gigabit Virtual Function Network Driver
[    1.156712] igbvf: Copyright (c) 2009 - 2012 Intel Corporation.
[    1.162852] sky2: driver version 1.30
[    1.166733] usbcore: registered new device driver r8152-cfgselector
[    1.172752] usbcore: registered new interface driver r8152
[    1.178551] VFIO - User Level meta-driver version: 0.3
[    1.185581] usbcore: registered new interface driver uas
[    1.190668] usbcore: registered new interface driver usb-storage
[    1.196670] usbcore: registered new interface driver usbserial_generic
[    1.203137] usbserial: USB Serial support registered for generic
[    1.209115] usbcore: registered new interface driver ftdi_sio
[    1.214837] usbserial: USB Serial support registered for FTDI USB Serial Device
[    1.222112] usbcore: registered new interface driver usb_serial_simple
[    1.228607] usbserial: USB Serial support registered for carelink
[    1.234682] usbserial: USB Serial support registered for flashloader
[    1.240999] usbserial: USB Serial support registered for funsoft
[    1.246980] usbserial: USB Serial support registered for google
[    1.252873] usbserial: USB Serial support registered for hp4x
[    1.258596] usbserial: USB Serial support registered for kaufmann
[    1.264659] usbserial: USB Serial support registered for libtransistor
[    1.271159] usbserial: USB Serial support registered for moto_modem
[    1.277405] usbserial: USB Serial support registered for motorola_tetra
[    1.283996] usbserial: USB Serial support registered for nokia
[    1.289792] usbserial: USB Serial support registered for novatel_gps
[    1.296126] usbserial: USB Serial support registered for siemens_mpi
[    1.302453] usbserial: USB Serial support registered for suunto
[    1.308345] usbserial: USB Serial support registered for vivopay
[    1.314319] usbserial: USB Serial support registered for zio
[    1.319961] usbcore: registered new interface driver usb_ehset_test
[    1.329184] i2c_dev: i2c /dev entries driver
[    1.336900] Bluetooth: HCI UART driver ver 2.3
[    1.341104] Bluetooth: HCI UART protocol H4 registered
[    1.346214] Bluetooth: HCI UART protocol BCSP registered
[    1.351515] Bluetooth: HCI UART protocol LL registered
[    1.356610] Bluetooth: HCI UART protocol ATH3K registered
[    1.361997] Bluetooth: HCI UART protocol Three-wire (H5) registered
[    1.368307] Bluetooth: HCI UART protocol Broadcom registered
[    1.373865] Bluetooth: HCI UART protocol QCA registered
[    1.380433] sdhci: Secure Digital Host Controller Interface driver
[    1.386375] sdhci: Copyright(c) Pierre Ossman
[    1.391109] Synopsys Designware Multimedia Card Interface Driver
[    1.397335] sdhci-pltfm: SDHCI platform and OF driver helper
[    1.406808] arm-scmi arm-scmi.0.auto: Using scmi_mailbox_transport
[    1.412785] arm-scmi arm-scmi.0.auto: SCMI max-rx-timeout: 5000ms
[    1.419241] arm-scmi arm-scmi.0.auto: SCMI Notifications - Core Enabled.
[    1.425867] arm-scmi arm-scmi.0.auto: SCMI Protocol v2.1 'NXP:IMX' Firmware version 0x226
[    1.436659] cpu cpu0: EM: invalid power: 0
[    1.440802] scmi-perf-domain scmi_dev.5: Initialized 13 performance domains
[    1.482097] scmi_module: Loaded SCMI Vendor Protocol 0x81 - NXP IMX 0
[    1.488902] arm-scmi arm-scmi.0.auto: NXP SM BBM Version 1.0
[    1.496287] scmi-imx-bbm-rtc scmi_dev.10: registered as rtc0
[    1.502718] scmi-imx-bbm-rtc scmi_dev.10: setting system clock to 1970-01-01T00:02:44 UTC (164)
[    1.511358] scmi-imx-bbm-key scmi_dev.11: key code is not specified, using default KEY_POWER
[    1.519720] input: scmi_dev.11 as /devices/platform/arm-scmi.0.auto/scmi_dev.11/input/input0
[    1.529000] scmi_module: Loaded SCMI Vendor Protocol 0x84 - NXP IMX 0
[    1.535268] arm-scmi arm-scmi.0.auto: NXP SM MISC Version 1.0
[    1.540961] arm-scmi arm-scmi.0.auto: i.MX MISC NUM DEV CTRL: 7, NUM BRD CTRL: 7,NUM Reason: 32
[    1.554665] fsl-se secure-enclave-0: Successfully registered ele-trng
[    1.555009] random: crng init done
[    1.560878] fsl-se secure-enclave-0: i.MX secure-enclave: hsm0 interface to firmware, configured.
[    1.573432] fsl-se secure-enclave-1: i.MX secure-enclave: v2x_dbg0 interface to firmware, configured.
[    1.582638] fsl-se secure-enclave-2: i.MX secure-enclave: v2x_sv0 interface to firmware, configured.
[    1.591738] fsl-se secure-enclave-3: i.MX secure-enclave: v2x_she0 interface to firmware, configured.
[    1.600815] SMCCC: SOC_ID: ARCH_SOC_ID not implemented, skipping ....
[    1.607401] usbcore: registered new interface driver usbhid
[    1.612740] usbhid: USB HID core driver
[    1.618390] neutron 4ab00004.imx95-neutron: created neutron device, name=neutron0
[    1.627248] remoteproc remoteproc0: neutron-rproc is available
[    1.647722] hw perfevents: enabled with armv8_cortex_a55 PMU driver, 7 (0,8000003f) counters available
[    1.659617]  cs_system_cfg: CoreSight Configuration manager initialised
[    1.670689] NET: Registered PF_LLC protocol family
[    1.675339] u32 classifier
[    1.677926]     input device check on
[    1.681555]     Actions configured
[    1.685395] NET: Registered PF_INET6 protocol family
[    1.690758] Segment Routing with IPv6
[    1.694225] In-situ OAM (IOAM) with IPv6
[    1.698099] NET: Registered PF_PACKET protocol family
[    1.703125] bridge: filtering via arp/ip/ip6tables is no longer available by default. Update your scripts to load br_netfilter if you need this.
[    1.716219] Bluetooth: RFCOMM TTY layer initialized
[    1.720864] Bluetooth: RFCOMM socket layer initialized
[    1.725998] Bluetooth: RFCOMM ver 1.11
[    1.729697] Bluetooth: BNEP (Ethernet Emulation) ver 1.3
[    1.734996] Bluetooth: BNEP filters: protocol multicast
[    1.740188] Bluetooth: BNEP socket layer initialized
[    1.745135] Bluetooth: HIDP (Human Interface Emulation) ver 1.2
[    1.751014] Bluetooth: HIDP socket layer initialized
[    1.756196] 8021q: 802.1Q VLAN Support v1.8
[    1.760150] 9pnet: Installing 9P2000 support
[    1.764474] Key type dns_resolver registered
[    1.768805] NET: Registered PF_VSOCK protocol family
[    1.787157] registered taskstats version 1
[    1.791144] Loading compiled-in X.509 certificates
[    1.801330] Demotion targets for Node 0: null
[    1.827809] usb_phy_generic usbphynop: dummy supplies not allowed for exclusive requests (id=vbus)
[    1.837623] platform 4ad30000.csi: Fixed dependency cycle(s) with /soc/bus@42000000/i2c@42530000/ap1302_mipi@3c
[    1.847585] i2c 2-003c: Fixed dependency cycle(s) with /soc/csi@4ad30000
[    1.854860] adp5585 2-0034: error -EIO: Failed to read device ID
[    1.860664] adp5585 2-0034: probe with driver adp5585 failed with error -5
[    1.867796] pca953x 2-0020: using no AI
[    1.871435] pca953x 2-0020: failed writing register: -5
[    1.876688] pca953x 2-0020: probe with driver pca953x failed with error -5
[    1.883774] leds-pca963x 2-0062: probe with driver leds-pca963x failed with error -5
[    1.891367] i2c i2c-2: LPI2C adapter registered
[    1.903067] i2c i2c-3: LPI2C adapter registered
[    1.908570] pca953x 4-0021: using no AI
[    1.912318] pca953x 4-0021: failed writing register: -5
[    1.917491] pca953x 4-0021: probe with driver pca953x failed with error -5
[    1.924379] i2c i2c-4: LPI2C adapter registered
[    1.929757] i2c i2c-5: LPI2C adapter registered
[    1.935761] i2c 6-0052: Fixed dependency cycle(s) with /soc/usb@4c010010/usb@4c100000
[    1.943617] i2c i2c-6: LPI2C adapter registered
[    1.948969] platform 4acf0000.dsi: Fixed dependency cycle(s) with /soc/bus@44000000/i2c@44350000/hdmi@3d
[    1.958269] platform hdmi-connector: Fixed dependency cycle(s) with /soc/bus@44000000/i2c@44350000/hdmi@3d
[    1.967952] i2c 1-003d: Fixed dependency cycle(s) with /soc/dsi@4acf0000
[    1.974521] i2c 1-003d: Fixed dependency cycle(s) with /hdmi-connector
[    1.981645] adp5585 1-0034: error -EIO: Failed to read device ID
[    1.987467] adp5585 1-0034: probe with driver adp5585 failed with error -5
[    1.994348] i2c i2c-1: LPI2C adapter registered
[    1.999929] imx8mq-usb-phy 4c1f0040.phy: supply vbus not found, using dummy regulator
[    2.012286] platform 4ad50000.isi: Fixed dependency cycle(s) with /soc/syscon@4ac10000/formatter@20
[    2.021176] platform 4ad30000.csi: Fixed dependency cycle(s) with /soc/syscon@4ac10000/formatter@20
[    2.030217] platform 4ac10000.syscon:formatter@20: Fixed dependency cycle(s) with /soc/isi@4ad50000
[    2.039175] platform 4ac10000.syscon:formatter@20: Fixed dependency cycle(s) with /soc/csi@4ad30000
[    2.050809] platform 4acf0000.dsi: Fixed dependency cycle(s) with /soc/syscon@4b010000/bridge@8
[    2.059352] platform 4b010000.syscon:bridge@8: Fixed dependency cycle(s) with /soc/dsi@4acf0000
[    2.068022] platform 4b010000.syscon:bridge@8: Fixed dependency cycle(s) with /soc/bridge@4b0d0000/channel@0
[    2.083666] 42590000.serial: ttyLP4 at MMIO 0x42590010 (irq = 110, base_baud = 1500000) is a FSL_LPUART
[    2.092970] serial serial0: tty port ttyLP4 registered
[    2.108381] printk: legacy console [ttyLP0] enabled010 (irq = 111, base_baud = 1500000) is a FSL_LPUART
[    2.108381] printk: legacy console [ttyLP0] enabled
[    2.118043] printk: legacy bootconsole [lpuart32] disabled
[    2.118043] printk: legacy bootconsole [lpuart32] disabled
[    2.133351] mali 4d900000.gpu: Kernel DDK version r53p0-00eac0
[    2.139516] mali 4d900000.gpu: GPU metrics tracepoint support enabled
[    2.147132] mali 4d900000.gpu: Register LUT 000a0a00 initialized for GPU arch 0x000a0c07
[    2.148996] imx95-mipi-dsi 4acf0000.dsi: Using Pixel Link0 as input source
[    2.155276] mali 4d900000.gpu: r0p0 status 1 not found in HW issues table;
[    2.168991] mali 4d900000.gpu: falling back to closest match: r0p0 status 0
[    2.175949] mali 4d900000.gpu: Execution proceeding normally with fallback match
[    2.182599] pci-host-generic 4ca00000.pcie: host bridge /soc/netc-blk-ctrl@4cde0000/pcie@4ca00000 ranges:
[    2.183340] mali 4d900000.gpu: GPU identified as 0x4 arch 10.12.7 r0p0 status 0
[    2.192972] pci-host-generic 4ca00000.pcie:      MEM 0x004cc00000..0x004ccdffff -> 0x004cc00000
[    2.200280] mali 4d900000.gpu: CSF_GPU_RESET_TIMEOUT is capped from 12400ms to 4500ms
[    2.208902] pci-host-generic 4ca00000.pcie:      MEM 0x004cd00000..0x004cd0ffff -> 0x004cd00000
[    2.216716] mali 4d900000.gpu: CSF_GPU_SUSPEND_TIMEOUT is capped from 6200ms to 4500ms
[    2.225410] pci-host-generic 4ca00000.pcie:      MEM 0x004cd20000..0x004cd7ffff -> 0x004cd20000
[    2.233306] mali 4d900000.gpu: KCPU_FENCE_SIGNAL_TIMEOUT is capped from 5000ms to 4500ms
[    2.241992] pci-host-generic 4ca00000.pcie:      MEM 0x004cd80000..0x004cddffff -> 0x004cd80000
[    2.250059] mali 4d900000.gpu: KBASE_PRFCNT_ACTIVE_TIMEOUT is capped from 24000ms to 4500ms
[    2.250065] mali 4d900000.gpu: KBASE_AS_INACTIVE_TIMEOUT is capped from 24000ms to 4500ms
[    2.250069] mali 4d900000.gpu: CSF_FIRMWARE_STOP_TIMEOUT is capped from 24000ms to 4500ms
[    2.258841] pci-host-generic 4ca00000.pcie: ECAM at [mem 0x4ca00000-0x4cafffff] for [bus 00]
[    2.267243] mali 4d900000.gpu: No priority control manager is configured
[    2.275505] pci-host-generic 4ca00000.pcie: PCI host bridge to bus 0002:00
[    2.283466] mali 4d900000.gpu: Large page support was disabled at compile-time!
[    2.283812] mali 4d900000.gpu: No memory group manager is configured
[    2.291927] pci_bus 0002:00: root bus resource [bus 00]
[    2.298622] mali 4d900000.gpu: Protected memory allocator not available
[    2.305478] pci_bus 0002:00: root bus resource [mem 0x4cc00000-0x4ccdffff]
[    2.313859] mali 4d900000.gpu: Using configured power model mali-tvax-power-model, and fallback mali-simple-power-model
[    2.319135] pci_bus 0002:00: root bus resource [mem 0x4cd00000-0x4cd0ffff pref]
[    2.324360] mali 4d900000.gpu: OPP 0 : opp_freq=500000000 core_mask=1
[    2.330959] pci_bus 0002:00: root bus resource [mem 0x4cd20000-0x4cd7ffff]
[    2.330965] pci_bus 0002:00: root bus resource [mem 0x4cd80000-0x4cddffff pref]
[    2.337845] mali 4d900000.gpu: OPP 1 : opp_freq=800000000 core_mask=1
[    2.348687] pci 0002:00:00.0: [1131:e101] type 00 class 0x020001 PCIe Root Complex Integrated Endpoint
[    2.355902] mali 4d900000.gpu: OPP 2 : opp_freq=1000000000 core_mask=1
[    2.362366] pci 0002:00:00.0: BAR 0 [mem 0x4cc00000-0x4cc3ffff 64bit]: from Enhanced Allocation, properties 0x0
[    2.371053] mali 4d900000.gpu: * MALI kbase_mmap_min_addr compiled to CONFIG_DEFAULT_MMAP_MIN_ADDR, no runtime update possible! *
[    2.376500] pci 0002:00:00.0: VF BAR 2 [mem 0x4cd20000-0x4cd2ffff 64bit]: from Enhanced Allocation, properties 0x4
[    2.382953] mali 4d900000.gpu: Probed as mali0
[    2.392231] pci 0002:00:00.0: VF BAR 4 [mem 0x4cd80000-0x4cd8ffff 64bit pref]: from Enhanced Allocation, properties 0x3
[    2.446107] pci 0002:00:00.0: PME# supported from D0 D3hot
[    2.451616] pci 0002:00:00.0: VF BAR 0 [mem 0x4cd20000-0x4cd3ffff 64bit]: contains BAR 0 for 2 VFs
[    2.460568] pci 0002:00:00.0: VF BAR 2 [mem 0x4cd80000-0x4cd9ffff 64bit pref]: contains BAR 2 for 2 VFs
[    2.470360] pci 0002:00:01.0: [1131:e001] type 00 class 0x080700 PCIe Root Complex Event Collector
[    2.479390] OF: /soc/netc-blk-ctrl@4cde0000/pcie@4ca00000: no msi-map translation for id 0x8 on (null)
[    2.489797] pci 0002:00:08.0: [1131:e101] type 00 class 0x020001 PCIe Root Complex Integrated Endpoint
[    2.499318] pci 0002:00:08.0: BAR 0 [mem 0x4cc40000-0x4cc7ffff 64bit]: from Enhanced Allocation, properties 0x0
[    2.509442] pci 0002:00:08.0: VF BAR 2 [mem 0x4cd40000-0x4cd4ffff 64bit]: from Enhanced Allocation, properties 0x4
[    2.519801] pci 0002:00:08.0: VF BAR 4 [mem 0x4cda0000-0x4cdaffff 64bit pref]: from Enhanced Allocation, properties 0x3
[    2.530608] pci 0002:00:08.0: PME# supported from D0 D3hot
[    2.536106] pci 0002:00:08.0: VF BAR 0 [mem 0x4cd40000-0x4cd5ffff 64bit]: contains BAR 0 for 2 VFs
[    2.545072] pci 0002:00:08.0: VF BAR 2 [mem 0x4cda0000-0x4cdbffff 64bit pref]: contains BAR 2 for 2 VFs
[    2.555575] pci 0002:00:10.0: [1131:e101] type 00 class 0x020001 PCIe Root Complex Integrated Endpoint
[    2.564970] pci 0002:00:10.0: BAR 0 [mem 0x4cc80000-0x4ccbffff 64bit]: from Enhanced Allocation, properties 0x0
[    2.575079] pci 0002:00:10.0: VF BAR 2 [mem 0x4cd60000-0x4cd6ffff 64bit]: from Enhanced Allocation, properties 0x4
[    2.585454] pci 0002:00:10.0: VF BAR 4 [mem 0x4cdc0000-0x4cdcffff 64bit pref]: from Enhanced Allocation, properties 0x3
[    2.596264] pci 0002:00:10.0: PME# supported from D0 D3hot
[    2.601832] pci 0002:00:10.0: VF BAR 0 [mem 0x4cd60000-0x4cd7ffff 64bit]: contains BAR 0 for 2 VFs
[    2.610837] pci 0002:00:10.0: VF BAR 2 [mem 0x4cdc0000-0x4cddffff 64bit pref]: contains BAR 2 for 2 VFs
[    2.621483] pci 0002:00:18.0: [1131:ee02] type 00 class 0x088001 PCIe Root Complex Integrated Endpoint
[    2.630872] pci 0002:00:18.0: BAR 0 [mem 0x4ccc0000-0x4ccdffff 64bit]: from Enhanced Allocation, properties 0x0
[    2.640977] pci 0002:00:18.0: BAR 2 [mem 0x4cd00000-0x4cd0ffff 64bit pref]: from Enhanced Allocation, properties 0x1
[    2.651524] pci 0002:00:18.0: PME# supported from D0 D3hot
[    2.657813] pci_bus 0002:00: resource 4 [mem 0x4cc00000-0x4ccdffff]
[    2.664129] pci_bus 0002:00: resource 5 [mem 0x4cd00000-0x4cd0ffff pref]
[    2.670832] pci_bus 0002:00: resource 6 [mem 0x4cd20000-0x4cd7ffff]
[    2.677099] pci_bus 0002:00: resource 7 [mem 0x4cd80000-0x4cddffff pref]
[    2.686658] pcieport 0002:00:01.0: PME: Signaling with IRQ 199
[    2.693219] pcieport 0002:00:01.0: AER: enabled with IRQ 199
[    2.701600] ptp_netc 0002:00:18.0: enabling device (0000 -> 0002)
[    2.708196] pps pps0: new PPS source ptp0
[    2.713213] pci-host-generic 4cb00000.pcie: host bridge /soc/netc-blk-ctrl@4cde0000/pcie@4cb00000 ranges:
[    2.722862] pci-host-generic 4cb00000.pcie:      MEM 0x004cce0000..0x004ccfffff -> 0x004cce0000
[    2.731675] pci-host-generic 4cb00000.pcie:      MEM 0x004cd10000..0x004cd1ffff -> 0x004cd10000
[    2.740441] pci-host-generic 4cb00000.pcie: ECAM at [mem 0x4cb00000-0x4cbfffff] for [bus 01]
[    2.748993] pci-host-generic 4cb00000.pcie: PCI host bridge to bus 0003:01
[    2.755880] pci_bus 0003:01: root bus resource [bus 01]
[    2.761104] pci_bus 0003:01: root bus resource [mem 0x4cce0000-0x4ccfffff]
[    2.767990] pci_bus 0003:01: root bus resource [mem 0x4cd10000-0x4cd1ffff pref]
[    2.775385] pci 0003:01:00.0: [1131:ee00] type 00 class 0x088001 PCIe Root Complex Integrated Endpoint
[    2.784726] pci 0003:01:00.0: BAR 0 [mem 0x4cce0000-0x4ccfffff 64bit]: from Enhanced Allocation, properties 0x0
[    2.794821] pci 0003:01:00.0: BAR 2 [mem 0x4cd10000-0x4cd1ffff 64bit pref]: from Enhanced Allocation, properties 0x1
[    2.805357] pci 0003:01:00.0: PME# supported from D0 D3hot
[    2.811171] pci 0003:01:01.0: [1131:e001] type 00 class 0x080700 PCIe Root Complex Event Collector
[    2.822763] pci_bus 0003:01: resource 4 [mem 0x4cce0000-0x4ccfffff]
[    2.829099] pci_bus 0003:01: resource 5 [mem 0x4cd10000-0x4cd1ffff pref]
[    2.835942] pciback 0003:01:01.0: of_irq_parse_pci: failed with rc=-22
[    2.842531] pcieport 0003:01:01.0: of_irq_parse_pci: failed with rc=-22
[    2.850359] i2c 6-0052: Fixed dependency cycle(s) with /soc/usb@4c010010/usb@4c100000
[    2.858378] platform 4c100000.usb: Fixed dependency cycle(s) with /soc/bus@42000000/i2c@426d0000/tcpc@52
[    2.869407] dwc3 4c100000.usb: Adding to iommu group 0
[    2.923587] vpu-ctrl 4c4c0000.vpu-ctrl: boot phys_addr: 0x00000000a0000000, dma_addr: 0x00000000a0000000, size: 0x100000
[    2.934510] vpu-ctrl 4c4c0000.vpu-ctrl: sram 0x0x00000000204c0000, 0x0x00000000204c0000, size 0x18000
[    2.952624] mmc0: SDHCI controller on 42850000.mmc [42850000.mmc] using ADMA
[    2.956262] pca953x 6-0022: using AI
[    2.963352] pca953x 6-0022: failed writing register: -5
[    2.968602] pca953x 6-0022: probe with driver pca953x failed with error -5
[    2.975811] pca953x 6-0023: using AI
[    2.986940] adv7511 1-003d: Probe failed. Remote port 'dsi@4acf0000' disabled
[    3.032741] mmc0: new HS400 Enhanced strobe MMC card at address 0001
[    3.037571] imx6q-pcie 4c380000.pcie: host bridge /soc/pcie@4c380000 ranges:
[    3.040440] mmcblk0: mmc0:0001 IY2964 58.3 GiB
[    3.046460] imx6q-pcie 4c380000.pcie:       IO 0x088ff00000..0x088fffffff -> 0x0000000000
[    3.059142] imx6q-pcie 4c380000.pcie:      MEM 0x0a10000000..0x0a1fffffff -> 0x0010000000
[    3.059159]  mmcblk0: p1 p2
[    3.059992] mmcblk0boot0: mmc0:0001 IY2964 4.00 MiB
[    3.076139] mmcblk0boot1: mmc0:0001 IY2964 4.00 MiB
[    3.082175] mmcblk0rpmb: mmc0:0001 IY2964 4.00 MiB, chardev (234:0)
[    3.086980] sdhci-esdhc-imx 42860000.mmc: Got CD GPIO
[    3.105046] [drm:drm_bridge_attach] *ERROR* failed to attach bridge /soc/dsi@4acf0000 to encoder None-40: -19
[    3.106065] imx6q-pcie 4c300000.pcie: host bridge /soc/pcie@4c300000 ranges:
[    3.115069] [drm:drm_bridge_attach] *ERROR* failed to attach bridge /soc/syscon@4b010000/bridge@8/ports/port@0 to encoder None-40: -19
[    3.122137] imx6q-pcie 4c300000.pcie:       IO 0x006ff00000..0x006fffffff -> 0x0000000000
[    3.124398] mmc1: SDHCI controller on 42860000.mmc [42860000.mmc] using ADMA
[    3.134248] [drm:drm_bridge_attach] *ERROR* failed to attach bridge /soc/bridge@4b0d0000/channel@0 to encoder None-40: -19
[    3.142381] imx6q-pcie 4c300000.pcie:      MEM 0x0910000000..0x091fffffff -> 0x0010000000
[    3.149413] imx95-dpu 4b400000.display-controller: [drm] *ERROR* failed to attach bridge to encoder for stream0: -19
[    3.182409] imx_usb 4c200000.usb: Adding to iommu group 1
[    3.188974] ci_hdrc ci_hdrc.0: stream 15 already in tree
[    3.197028] ci_hdrc ci_hdrc.0: EHCI Host Controller
[    3.201970] ci_hdrc ci_hdrc.0: new USB bus registered, assigned bus number 1
[    3.219914] ci_hdrc ci_hdrc.0: USB 2.0 started, EHCI 1.00
[    3.226688] hub 1-0:1.0: USB hub found
[    3.230601] hub 1-0:1.0: 1 port detected
[    3.242009] sdhci-esdhc-imx 428b0000.mmc: allocated mmc-pwrseq
[    3.279813] mmc2: SDHCI controller on 428b0000.mmc [428b0000.mmc] using ADMA
[    3.287501] imx6q-pcie 4c380000.pcie: iATU: unroll T, 128 ob, 128 ib, align 4K, limit 1024G
[    3.318401] mmc2: new high speed SDIO card at address 0001
[    3.352056] fsl_enetc_mdio 0003:01:00.0: enabling device (0000 -> 0002)
[    3.368102] imx6q-pcie 4c300000.pcie: iATU: unroll T, 128 ob, 128 ib, align 4K, limit 1024G
[    3.391955] imx6q-pcie 4c380000.pcie: PCIe Gen.1 x1 link up
[    3.480069] usb 1-1: new high-speed USB device number 2 using ci_hdrc
[    3.491957] imx6q-pcie 4c380000.pcie: PCIe Gen.3 x1 link up
[    3.497639] imx6q-pcie 4c380000.pcie: Link up, Gen3
[    3.502551] imx6q-pcie 4c380000.pcie: PCIe Gen.3 x1 link up
[    3.508324] imx6q-pcie 4c380000.pcie: PCI host bridge to bus 0001:00
[    3.514686] pci_bus 0001:00: root bus resource [bus 00-ff]
[    3.520189] pci_bus 0001:00: root bus resource [io  0x0000-0xfffff]
[    3.526459] pci_bus 0001:00: root bus resource [mem 0xa10000000-0xa1fffffff] (bus address [0x10000000-0x1fffffff])
[    3.536818] pci 0001:00:00.0: [1131:0000] type 01 class 0x060400 PCIe Root Port
[    3.544151] pci 0001:00:00.0: BAR 0 [mem 0x00000000-0x000fffff]
[    3.550098] pci 0001:00:00.0: ROM [mem 0x00000000-0x0000ffff pref]
[    3.556310] pci 0001:00:00.0: PCI bridge to [bus 01-ff]
[    3.561540] pci 0001:00:00.0:   bridge window [io  0x0000-0x0fff]
[    3.567645] pci 0001:00:00.0:   bridge window [mem 0x00000000-0x000fffff]
[    3.574438] pci 0001:00:00.0:   bridge window [mem 0x00000000-0x000fffff 64bit pref]
[    3.582209] pci 0001:00:00.0: supports D1 D2
[    3.586491] pci 0001:00:00.0: PME# supported from D0 D1 D3hot
[    3.595087] pci 0001:01:00.0: [9999:eeee] type 00 class 0xff0000 PCIe Endpoint
[    3.602435] pci 0001:01:00.0: BAR 0 [mem 0xa10200000-0xa103fffff]
[    3.608549] pci 0001:01:00.0: BAR 1 [mem 0xa10100000-0xa1010ffff]
[    3.614755] pci 0001:01:00.0: ROM [mem 0x00000000-0x0000ffff pref]
[    3.621123] pci 0001:01:00.0: supports D1 D2
[    3.625393] pci 0001:01:00.0: PME# supported from D0 D1 D3hot
[    3.636007] pci 0001:00:00.0: BAR 0 [mem 0xa10000000-0xa100fffff]: assigned
[    3.643026] pci 0001:00:00.0: bridge window [mem 0xa10100000-0xa103fffff]: assigned
[    3.650766] pci 0001:00:00.0: ROM [mem 0xa10400000-0xa1040ffff pref]: assigned
[    3.658058] pci 0001:01:00.0: BAR 0 [mem 0xa10200000-0xa103fffff]: assigned
[    3.665034] pci 0001:01:00.0: BAR 1 [mem 0xa10100000-0xa1010ffff]: assigned
[    3.672026] pci 0001:01:00.0: ROM [mem 0xa10110000-0xa1011ffff pref]: assigned
[    3.679369] pci 0001:00:00.0: PCI bridge to [bus 01-ff]
[    3.684711] pci 0001:00:00.0:   bridge window [mem 0xa10100000-0xa103fffff]
[    3.691767] hub 1-1:1.0: USB hub found
[    3.695548] pci_bus 0001:00: resource 4 [io  0x0000-0xfffff]
[    3.701226] hub 1-1:1.0: 4 ports detected
[    3.705266] pci_bus 0001:00: resource 5 [mem 0xa10000000-0xa1fffffff]
[    3.711763] pci_bus 0001:01: resource 1 [mem 0xa10100000-0xa103fffff]
[    3.718498] pciback 0001:00:00.0: Adding to iommu group 2
[    3.725681] pcieport 0001:00:00.0: PME: Signaling with IRQ 223
[    3.731919] pcieport 0001:00:00.0: AER: enabled with IRQ 223
[    3.737757] pciback 0001:01:00.0: Adding to iommu group 2
[    4.344164] imx6q-pcie 4c300000.pcie: Phy link never came up
[    5.308146] imx6q-pcie 4c300000.pcie: Phy link never came up
[    5.313982] imx6q-pcie 4c300000.pcie: PCI host bridge to bus 0000:00
[    5.320347] pci_bus 0000:00: root bus resource [bus 00-ff]
[    5.325871] pci_bus 0000:00: root bus resource [io  0x100000-0x1fffff] (bus address [0x0000-0xfffff])
[    5.335100] pci_bus 0000:00: root bus resource [mem 0x910000000-0x91fffffff] (bus address [0x10000000-0x1fffffff])
[    5.345467] pci 0000:00:00.0: [1131:0000] type 01 class 0x060400 PCIe Root Port
[    5.352778] pci 0000:00:00.0: BAR 0 [mem 0x00000000-0x000fffff]
[    5.358703] pci 0000:00:00.0: ROM [mem 0x00000000-0x0000ffff pref]
[    5.364889] pci 0000:00:00.0: PCI bridge to [bus 01-ff]
[    5.370118] pci 0000:00:00.0:   bridge window [io  0x100000-0x100fff]
[    5.372347] Aquantia AQR113C 0003:01:00.0:08: probe with driver Aquantia AQR113C failed with error -22
[    5.376561] pci 0000:00:00.0:   bridge window [mem 0x00000000-0x000fffff]
[    5.392343] NXP C45 TJA1120 0003:01:00.0:07: the phy does not support MACsec
[    5.392647] pci 0000:00:00.0:   bridge window [mem 0x00000000-0x000fffff 64bit pref]
[    5.407458] pci 0000:00:00.0: supports D1 D2
[    5.411738] pci 0000:00:00.0: PME# supported from D0 D1 D3hot
[    5.420391] pci 0000:00:00.0: BAR 0 [mem 0x910000000-0x9100fffff]: assigned
[    5.427400] pci 0000:00:00.0: ROM [mem 0x910100000-0x91010ffff pref]: assigned
[    5.434627] pci 0000:00:00.0: PCI bridge to [bus 01-ff]
[    5.439861] pci_bus 0000:00: resource 4 [io  0x100000-0x1fffff]
[    5.445784] pci_bus 0000:00: resource 5 [mem 0x910000000-0x91fffffff]
[    5.452399] pciback 0000:00:00.0: Adding to iommu group 3
[    5.459634] pcieport 0000:00:00.0: PME: Signaling with IRQ 225
[    5.465837] pcieport 0000:00:00.0: AER: enabled with IRQ 225
[    5.507923] fsl_enetc4 0002:00:00.0: enabling device (0000 -> 0002)
[    6.023959] usb 1-1.3: new high-speed USB device number 3 using ci_hdrc
[    6.660109] usb-storage 1-1.3:1.0: USB Mass Storage device detected
[    6.667006] scsi host0: usb-storage 1-1.3:1.0
[    7.980019] scsi 0:0:0:0: Direct-Access     Kingston DT 101 G2        1.00 PQ: 0 ANSI: 4
[    8.448094] sd 0:0:0:0: [sda] 15131636 512-byte logical blocks: (7.75 GB/7.21 GiB)
[    8.527975] sd 0:0:0:0: [sda] Write Protect is off
[    8.612054] sd 0:0:0:0: [sda] Write cache: disabled, read cache: enabled, doesn't support DPO or FUA
[   26.607970] rcu: INFO: rcu_preempt detected stalls on CPUs/tasks:
[   26.614065] rcu:     0-....: (0 ticks this GP) idle=4ffc/1/0x4000000000000004 softirq=152/10158 fqs=2486
[   26.623272] rcu:     (detected by 3, t=5255 jiffies, g=-803, q=491 ncpus=6)
[   26.629965] Sending NMI from CPU 3 to CPUs 0:
[   26.629973] NMI backtrace for cpu 0
[   26.637803] CPU: 0 UID: 0 PID: 0 Comm: swapper/0 Not tainted 6.12.3-lts-next-g37d02f4dcbbe-dirty #1
[   26.646837] Hardware name: NXP i.MX95 19X19 board (DT)
[   26.651962] pstate: 40400009 (nZcv daif +PAN -UAO -TCO -DIT -SSBS BTYPE=--)
[   26.658912] pc : handle_softirqs+0xb4/0x360
[   26.663089] lr : handle_softirqs+0x80/0x360
[   26.667271] sp : ffff800080003f40
[   26.670577] x29: ffff800080003f40 x28: ffff8000823a8000 x27: 00000000fd50dc10
[   26.677709] x26: ffff8000824a50c0 x25: 0000000000000000 x24: 00000000de70e408
[   26.684842] x23: 0000000080400009 x22: 0000000000000200 x21: ffff8000824a3d20
[   26.691975] x20: ffff8000800100e8 x19: 0000000000000000 x18: 00000000e4bb62d2
[   26.699099] x17: ffff80037bb84000 x16: ffff800080000000 x15: 0000000000000000
[   26.706232] x14: ffff8000824b5540 x13: 0000000000000358 x12: 0000000000000001
[   26.713356] x11: 0000000000000040 x10: ffff000080304170 x9 : ffff80008005cae8
[   26.720488] x8 : ffff0000820b54b0 x7 : 0000000000000000 x6 : 00000000f12b8298
[   26.727612] x5 : 00ffffffffffffff x4 : 0000000000000015 x3 : 0000000000c1d34d
[   26.734736] x2 : 0000000633266d00 x1 : ffff80037bb84000 x0 : ffff8000823bb580
[   26.741876] Call trace:
[   26.744320]  handle_softirqs+0xb4/0x360
[   26.748146]  __do_softirq+0x1c/0x28
[   26.751634]  ____do_softirq+0x18/0x30
[   26.755285]  call_on_irq_stack+0x24/0x58
[   26.759202]  do_softirq_own_stack+0x24/0x50
[   26.763386]  irq_exit_rcu+0x90/0xd0
[   26.766864]  el1_interrupt+0x38/0x68
[   26.770434]  el1h_64_irq_handler+0x18/0x28
[   26.774516]  el1h_64_irq+0x64/0x68
[   26.777905]  cpuidle_enter_state+0x2c0/0x4e0
[   26.782160]  cpuidle_enter+0x40/0x60
[   26.785731]  do_idle+0x1ec/0x268
[   26.788962]  cpu_startup_entry+0x3c/0x50
[   26.792879]  rest_init+0xe4/0xf0
[   26.796096]  start_kernel+0x740/0x748
[   26.799760]  __primary_switched+0x80/0x90
[   27.891942] rcu: INFO: rcu_preempt detected expedited stalls on CPUs/tasks: { 0-.... } 5594 jiffies s: 57 root: 0x1/.
[   27.902554] rcu: blocking rcu_node structures (internal RCU debug):
[   27.908822] Sending NMI from CPU 1 to CPUs 0:
[   27.913177] NMI backtrace for cpu 0
[   27.916656] CPU: 0 UID: 0 PID: 0 Comm: swapper/0 Not tainted 6.12.3-lts-next-g37d02f4dcbbe-dirty #1
[   27.925684] Hardware name: NXP i.MX95 19X19 board (DT)
[   27.930807] pstate: 60400009 (nZCv daif +PAN -UAO -TCO -DIT -SSBS BTYPE=--)
[   27.937756] pc : rcu_core+0x10c/0xa00
[   27.941422] lr : rcu_core+0xa0/0xa00
[   27.944993] sp : ffff800080003e80
[   27.948304] x29: ffff800080003ed0 x28: ffff8000823a8000 x27: ffff0003fdf410b8
[   27.955427] x26: ffff8000824a50c0 x25: ffff80008253a900 x24: ffff8000823bd040
[   27.962552] x23: ffff80037bb84000 x22: ffff8000824a8c98 x21: ffff0003fdf41040
[   27.969676] x20: ffff80008253a900 x19: ffff8000823bd051 x18: 00000000e4bb62d2
[   27.976800] x17: ffff80037bb84000 x16: ffff800080000000 x15: 0000000000000000
[   27.983923] x14: ffff8000824b5540 x13: 0000000000000358 x12: 0000000000000001
[   27.991047] x11: 0000000000000040 x10: ffff000080304170 x9 : ffff800080101f70
[   27.998172] x8 : ffff0000820b54b0 x7 : 0000000000000000 x6 : 00000000f12b8298
[   28.005296] x5 : 00ffffffffffffff x4 : 0000000000000015 x3 : 0000000000c1d34d
[   28.012419] x2 : 0000000000000000 x1 : fffffffffffffcdd x0 : 0000000000000001
[   28.019544] Call trace:
[   28.021985]  rcu_core+0x10c/0xa00
[   28.025290]  rcu_core_si+0x18/0x38
[   28.028687]  handle_softirqs+0x128/0x360
[   28.032611]  __do_softirq+0x1c/0x28
[   28.036088]  ____do_softirq+0x18/0x30
[   28.039746]  call_on_irq_stack+0x24/0x58
[   28.043663]  do_softirq_own_stack+0x24/0x50
[   28.047832]  irq_exit_rcu+0x90/0xd0
[   28.051316]  el1_interrupt+0x38/0x68
[   28.054886]  el1h_64_irq_handler+0x18/0x28
[   28.058977]  el1h_64_irq+0x64/0x68
[   28.062374]  cpuidle_enter_state+0x2c0/0x4e0
[   28.066638]  cpuidle_enter+0x40/0x60
[   28.070209]  do_idle+0x1ec/0x268
[   28.073433]  cpu_startup_entry+0x3c/0x50
[   28.077350]  rest_init+0xe4/0xf0
[   28.080565]  start_kernel+0x740/0x748
[   28.084223]  __primary_switched+0x80/0x90
[   89.807964] rcu: INFO: rcu_preempt detected stalls on CPUs/tasks:
[   89.814049] rcu:     0-....: (0 ticks this GP) idle=4ffc/1/0x4000000000000004 softirq=152/43097 fqs=9931
[   89.823258] rcu:     (detected by 2, t=21055 jiffies, g=-803, q=491 ncpus=6)
[   89.830030] Sending NMI from CPU 2 to CPUs 0:
[   89.830036] NMI backtrace for cpu 0
[   89.837873] CPU: 0 UID: 0 PID: 0 Comm: swapper/0 Not tainted 6.12.3-lts-next-g37d02f4dcbbe-dirty #1
[   89.846902] Hardware name: NXP i.MX95 19X19 board (DT)
[   89.852025] pstate: 60400009 (nZCv daif +PAN -UAO -TCO -DIT -SSBS BTYPE=--)
[   89.858981] pc : note_gp_changes+0x48/0xa0
[   89.863074] lr : rcu_core+0xa0/0xa00
[   89.866646] sp : ffff800080003e50
[   89.869947] x29: ffff800080003e50 x28: ffff8000823a8000 x27: ffff8000824b5540
[   89.877079] x26: ffff8000824a50c0 x25: ffff8000824a8c98 x24: ffff8000823bd040
[   89.884212] x23: ffff80037bb84000 x22: ffff8000824a8c98 x21: 0000000000000000
[   89.891345] x20: ffff80008253a900 x19: ffff0003fdf41040 x18: 00000000e4bb62d2
[   89.898477] x17: ffff80037bb84000 x16: ffff800080000000 x15: 0000000000000000
[   89.905610] x14: ffff8000824b5540 x13: 0000000000000358 x12: 0000000000000001
[   89.912743] x11: 0000000000000040 x10: ffff000080304170 x9 : ffff800080101f70
[   89.919875] x8 : ffff0000820b54b0 x7 : 0000000000000000 x6 : 00000000f12b8298
[   89.927008] x5 : 00ffffffffffffff x4 : 0000000000000015 x3 : 0000000000c1d34d
[   89.934132] x2 : 0000000000000000 x1 : fffffffffffffcdd x0 : 0000000000000000
[   89.941263] Call trace:
[   89.943707]  note_gp_changes+0x48/0xa0
[   89.947444]  rcu_core+0xa0/0xa00
[   89.950668]  rcu_core_si+0x18/0x38
[   89.954072]  handle_softirqs+0x128/0x360
[   89.957983]  __do_softirq+0x1c/0x28
[   89.961467]  ____do_softirq+0x18/0x30
[   89.965124]  call_on_irq_stack+0x24/0x58
[   89.969041]  do_softirq_own_stack+0x24/0x50
[   89.973210]  irq_exit_rcu+0x90/0xd0
[   89.976694]  el1_interrupt+0x38/0x68
[   89.980265]  el1h_64_irq_handler+0x18/0x28
[   89.984355]  el1h_64_irq+0x64/0x68
[   89.987753]  cpuidle_enter_state+0x2c0/0x4e0
[   89.992017]  cpuidle_enter+0x40/0x60
[   89.995587]  do_idle+0x1ec/0x268
[   89.998811]  cpu_startup_entry+0x3c/0x50
[   90.002729]  rest_init+0xe4/0xf0
[   90.005953]  start_kernel+0x740/0x748
[   90.009617]  __primary_switched+0x80/0x90
[   91.375993] rcu: INFO: rcu_preempt detected expedited stalls on CPUs/tasks: { 0-.... } 21466 jiffies s: 57 root: 0x1/.
[   91.386692] rcu: blocking rcu_node structures (internal RCU debug):
[   91.392948] Sending NMI from CPU 3 to CPUs 0:
[   91.397300] NMI backtrace for cpu 0
[   91.400786] CPU: 0 UID: 0 PID: 0 Comm: swapper/0 Not tainted 6.12.3-lts-next-g37d02f4dcbbe-dirty #1
[   91.409821] Hardware name: NXP i.MX95 19X19 board (DT)
[   91.414952] pstate: 60400009 (nZCv daif +PAN -UAO -TCO -DIT -SSBS BTYPE=--)
[   91.421895] pc : rcu_core+0x110/0xa00
[   91.425553] lr : rcu_core+0xa0/0xa00
[   91.429123] sp : ffff800080003e80
[   91.432434] x29: ffff800080003ed0 x28: ffff8000823a8000 x27: ffff0003fdf410b8
[   91.439558] x26: ffff8000824a50c0 x25: ffff80008253a900 x24: ffff8000823bd040
[   91.446691] x23: ffff80037bb84000 x22: ffff8000824a8c98 x21: ffff0003fdf41040
[   91.453823] x20: ffff80008253a900 x19: ffff8000823bd051 x18: 00000000e4bb62d2
[   91.460956] x17: ffff80037bb84000 x16: ffff800080000000 x15: 0000000000000000
[   91.468089] x14: ffff8000824b5540 x13: 0000000000000358 x12: 0000000000000001
[   91.475221] x11: 0000000000000040 x10: ffff000080304170 x9 : ffff800080101f70
[   91.482354] x8 : ffff0000820b54b0 x7 : 0000000000000000 x6 : 00000000f12b8298
[   91.489487] x5 : 00ffffffffffffff x4 : 0000000000000015 x3 : 0000000000c1d34d
[   91.496619] x2 : 0000000000000000 x1 : fffffffffffffcdd x0 : fffffffffffffcdd
[   91.503759] Call trace:
[   91.506196]  rcu_core+0x110/0xa00
[   91.509507]  rcu_core_si+0x18/0x38
[   91.512904]  handle_softirqs+0x128/0x360
[   91.516813]  __do_softirq+0x1c/0x28
[   91.520297]  ____do_softirq+0x18/0x30
[   91.523945]  call_on_irq_stack+0x24/0x58
[   91.527863]  do_softirq_own_stack+0x24/0x50
[   91.532040]  irq_exit_rcu+0x90/0xd0
[   91.535515]  el1_interrupt+0x38/0x68
[   91.539086]  el1h_64_irq_handler+0x18/0x28
[   91.543177]  el1h_64_irq+0x64/0x68
[   91.546565]  cpuidle_enter_state+0x2c0/0x4e0
[   91.550829]  cpuidle_enter+0x40/0x60
[   91.554400]  do_idle+0x1ec/0x268
[   91.557615]  cpu_startup_entry+0x3c/0x50
[   91.561533]  rest_init+0xe4/0xf0
[   91.564748]  start_kernel+0x740/0x748
[   91.568405]  __primary_switched+0x80/0x90
[  153.031964] rcu: INFO: rcu_preempt detected stalls on CPUs/tasks:
[  153.038050] rcu:     0-....: (0 ticks this GP) idle=4ffc/1/0x4000000000000004 softirq=152/74344 fqs=17458
[  153.047345] rcu:     (detected by 5, t=36861 jiffies, g=-803, q=491 ncpus=6)
[  153.054123] Sending NMI from CPU 5 to CPUs 0:
[  153.054131] NMI backtrace for cpu 0
[  153.061959] CPU: 0 UID: 0 PID: 0 Comm: swapper/0 Not tainted 6.12.3-lts-next-g37d02f4dcbbe-dirty #1
[  153.070995] Hardware name: NXP i.MX95 19X19 board (DT)
[  153.076127] pstate: 20400009 (nzCv daif +PAN -UAO -TCO -DIT -SSBS BTYPE=--)
[  153.083077] pc : rcu_core+0x80/0xa00
[  153.086650] lr : rcu_core_si+0x18/0x38
[  153.090396] sp : ffff800080003e80
[  153.093696] x29: ffff800080003ed0 x28: ffff8000823a8000 x27: ffff8000824b5540
[  153.100828] x26: ffff8000824a50c0 x25: ffff8000824a8c98 x24: ffff8000823bd040
[  153.107961] x23: ffff80037bb84000 x22: ffff8000824a8c98 x21: ffff0003fdf41040
[  153.115085] x20: ffff80008253a900 x19: ffff8000823bd040 x18: 00000000e4bb62d2
[  153.122218] x17: ffff80037bb84000 x16: ffff800080000000 x15: 0000000000000000
[  153.129350] x14: ffff8000824b5540 x13: 0000000000000358 x12: 0000000000000001
[  153.136483] x11: 0000000000000040 x10: ffff000080304170 x9 : ffff800080102be8
[  153.143616] x8 : ffff0000820b54b0 x7 : 0000000000000000 x6 : 00000000f12b8298
[  153.150748] x5 : 00ffffffffffffff x4 : 0000000000000015 x3 : 0000000000c1d34d
[  153.157881] x2 : 0000000000000000 x1 : 0000000000000000 x0 : 0000000000000001
[  153.165021] Call trace:
[  153.167464]  rcu_core+0x80/0xa00
[  153.170682]  rcu_core_si+0x18/0x38
[  153.174079]  handle_softirqs+0x128/0x360
[  153.178003]  __do_softirq+0x1c/0x28
[  153.181480]  ____do_softirq+0x18/0x30
[  153.185138]  call_on_irq_stack+0x24/0x58
[  153.189055]  do_softirq_own_stack+0x24/0x50
[  153.193224]  irq_exit_rcu+0x90/0xd0
[  153.196708]  el1_interrupt+0x38/0x68
[  153.200278]  el1h_64_irq_handler+0x18/0x28
[  153.204360]  el1h_64_irq+0x64/0x68
[  153.207749]  cpuidle_enter_state+0x2c0/0x4e0
[  153.212004]  cpuidle_enter+0x40/0x60
[  153.215575]  do_idle+0x1ec/0x268
[  153.218799]  cpu_startup_entry+0x3c/0x50
[  153.222714]  rest_init+0xe4/0xf0
[  153.225932]  start_kernel+0x740/0x748
[  153.229589]  __primary_switched+0x80/0x90
[  154.863986] rcu: INFO: rcu_preempt detected expedited stalls on CPUs/tasks: { 0-.... } 37338 jiffies s: 57 root: 0x1/.
[  154.874684] rcu: blocking rcu_node structures (internal RCU debug):
[  154.880942] Sending NMI from CPU 1 to CPUs 0:
[  154.885294] NMI backtrace for cpu 0
[  154.888773] CPU: 0 UID: 0 PID: 0 Comm: swapper/0 Not tainted 6.12.3-lts-next-g37d02f4dcbbe-dirty #1
[  154.897801] Hardware name: NXP i.MX95 19X19 board (DT)
[  154.902923] pstate: 60400009 (nZCv daif +PAN -UAO -TCO -DIT -SSBS BTYPE=--)
[  154.909865] pc : rcu_core+0x110/0xa00
[  154.913522] lr : rcu_core+0xa0/0xa00
[  154.917093] sp : ffff800080003e80
[  154.920404] x29: ffff800080003ed0 x28: ffff8000823a8000 x27: ffff0003fdf410b8
[  154.927528] x26: ffff8000824a50c0 x25: ffff80008253a900 x24: ffff8000823bd040
[  154.934652] x23: ffff80037bb84000 x22: ffff8000824a8c98 x21: ffff0003fdf41040
[  154.941776] x20: ffff80008253a900 x19: ffff8000823bd051 x18: 00000000e4bb62d2
[  154.948900] x17: ffff80037bb84000 x16: ffff800080000000 x15: 0000000000000000
[  154.956024] x14: ffff8000824b5540 x13: 0000000000000358 x12: 0000000000000001
[  154.963148] x11: 0000000000000040 x10: ffff000080304170 x9 : ffff800080101f70
[  154.970272] x8 : ffff0000820b54b0 x7 : 0000000000000000 x6 : 00000000f12b8298
[  154.977396] x5 : 00ffffffffffffff x4 : 0000000000000015 x3 : 0000000000c1d34d
[  154.984520] x2 : 0000000000000000 x1 : fffffffffffffcdd x0 : fffffffffffffcdd
[  154.991644] Call trace:
[  154.994079]  rcu_core+0x110/0xa00
[  154.997390]  rcu_core_si+0x18/0x38
[  155.000787]  handle_softirqs+0x128/0x360
[  155.004696]  __do_softirq+0x1c/0x28
[  155.008180]  ____do_softirq+0x18/0x30
[  155.011828]  call_on_irq_stack+0x24/0x58
[  155.015752]  do_softirq_own_stack+0x24/0x50
[  155.019923]  irq_exit_rcu+0x90/0xd0
[  155.023398]  el1_interrupt+0x38/0x68
[  155.026960]  el1h_64_irq_handler+0x18/0x28
[  155.031051]  el1h_64_irq+0x64/0x68
[  155.034440]  cpuidle_enter_state+0x2c0/0x4e0
[  155.038704]  cpuidle_enter+0x40/0x60
[  155.042275]  do_idle+0x1ec/0x268
[  155.045490]  cpu_startup_entry+0x3c/0x50
[  155.049407]  rest_init+0xe4/0xf0
[  155.052622]  start_kernel+0x740/0x748
[  155.056280]  __primary_switched+0x80/0x90
[  216.251877] rcu: INFO: rcu_preempt detected stalls on CPUs/tasks:
[  216.257974] rcu:     0-....: (0 ticks this GP) idle=4ffc/1/0x4000000000000004 softirq=152/106820 fqs=24819
[  216.267359] rcu:     (detected by 3, t=52666 jiffies, g=-803, q=491 ncpus=6)
[  216.274137] Sending NMI from CPU 3 to CPUs 0:
[  216.274143] NMI backtrace for cpu 0
[  216.281973] CPU: 0 UID: 0 PID: 0 Comm: swapper/0 Not tainted 6.12.3-lts-next-g37d02f4dcbbe-dirty #1
[  216.291010] Hardware name: NXP i.MX95 19X19 board (DT)
[  216.296141] pstate: 40400009 (nZcv daif +PAN -UAO -TCO -DIT -SSBS BTYPE=--)
[  216.303084] pc : handle_softirqs+0xb4/0x360
[  216.307261] lr : handle_softirqs+0x80/0x360
[  216.311439] sp : ffff800080003f40
[  216.314749] x29: ffff800080003f40 x28: ffff8000823a8000 x27: 00000000fd50dc10
[  216.321882] x26: ffff8000824a50c0 x25: 0000000000000000 x24: 00000000de70e408
[  216.329015] x23: 0000000080400009 x22: 0000000000000200 x21: ffff8000824a3d20
[  216.336139] x20: ffff8000800100e8 x19: 0000000000000000 x18: 00000000e4bb62d2
[  216.343263] x17: ffff80037bb84000 x16: ffff800080000000 x15: 0000000000000000
[  216.350387] x14: ffff8000824b5540 x13: 0000000000000358 x12: 0000000000000001
[  216.357519] x11: 0000000000000040 x10: ffff000080304170 x9 : ffff80008005cae8
[  216.364644] x8 : ffff0000820b54b0 x7 : 0000000000000000 x6 : 00000000f12b8298
[  216.371767] x5 : 00ffffffffffffff x4 : 0000000000000015 x3 : 0000000000c1d34d
[  216.378891] x2 : 000000325ad03362 x1 : ffff80037bb84000 x0 : ffff8000823bb580
[  216.386016] Call trace:
[  216.388460]  handle_softirqs+0xb4/0x360
[  216.392282]  __do_softirq+0x1c/0x28
[  216.395757]  ____do_softirq+0x18/0x30
[  216.399414]  call_on_irq_stack+0x24/0x58
[  216.403329]  do_softirq_own_stack+0x24/0x50
[  216.407500]  irq_exit_rcu+0x90/0xd0
[  216.410984]  el1_interrupt+0x38/0x68
[  216.414555]  el1h_64_irq_handler+0x18/0x28
[  216.418637]  el1h_64_irq+0x64/0x68
[  216.422034]  cpuidle_enter_state+0x2c0/0x4e0
[  216.426290]  cpuidle_enter+0x40/0x60
[  216.429860]  do_idle+0x1ec/0x268
[  216.433084]  cpu_startup_entry+0x3c/0x50
[  216.437002]  rest_init+0xe4/0xf0
[  216.440226]  start_kernel+0x740/0x748
[  216.443883]  __primary_switched+0x80/0x90
[  218.351987] rcu: INFO: rcu_preempt detected expedited stalls on CPUs/tasks: { 0-.... } 53210 jiffies s: 57 root: 0x1/.
[  218.362688] rcu: blocking rcu_node structures (internal RCU debug):
[  218.368952] Sending NMI from CPU 4 to CPUs 0:
[  218.373307] NMI backtrace for cpu 0
[  218.376786] CPU: 0 UID: 0 PID: 0 Comm: swapper/0 Not tainted 6.12.3-lts-next-g37d02f4dcbbe-dirty #1
[  218.385821] Hardware name: NXP i.MX95 19X19 board (DT)
[  218.390952] pstate: 40400009 (nZcv daif +PAN -UAO -TCO -DIT -SSBS BTYPE=--)
[  218.397895] pc : handle_softirqs+0xb4/0x360
[  218.402073] lr : handle_softirqs+0x80/0x360
[  218.406250] sp : ffff800080003f40
[  218.409561] x29: ffff800080003f40 x28: ffff8000823a8000 x27: 00000000fd50dc10
[  218.416694] x26: ffff8000824a50c0 x25: 0000000000000000 x24: 00000000de70e408
[  218.423826] x23: 0000000080400009 x22: 0000000000000200 x21: ffff8000824a3d20
[  218.430959] x20: ffff8000800100e8 x19: 0000000000000000 x18: 00000000e4bb62d2
[  218.438091] x17: ffff80037bb84000 x16: ffff800080000000 x15: 0000000000000000
[  218.445215] x14: ffff8000824b5540 x13: 0000000000000358 x12: 0000000000000001
[  218.452339] x11: 0000000000000040 x10: ffff000080304170 x9 : ffff80008005cae8
[  218.459472] x8 : ffff0000820b54b0 x7 : 0000000000000000 x6 : 00000000f12b8298
[  218.466605] x5 : 00ffffffffffffff x4 : 0000000000000015 x3 : 0000000000c1d34d
[  218.473737] x2 : 00000032d7fbb48f x1 : ffff80037bb84000 x0 : ffff8000823bb580
[  218.480862] Call trace:
[  218.483297]  handle_softirqs+0xb4/0x360
[  218.487119]  __do_softirq+0x1c/0x28
[  218.490603]  ____do_softirq+0x18/0x30
[  218.494260]  call_on_irq_stack+0x24/0x58
[  218.498169]  do_softirq_own_stack+0x24/0x50
[  218.502337]  irq_exit_rcu+0x90/0xd0
[  218.505821]  el1_interrupt+0x38/0x68
[  218.509392]  el1h_64_irq_handler+0x18/0x28
[  218.513474]  el1h_64_irq+0x64/0x68
[  218.516872]  cpuidle_enter_state+0x2c0/0x4e0
[  218.521127]  cpuidle_enter+0x40/0x60
[  218.524698]  do_idle+0x1ec/0x268
[  218.527922]  cpu_startup_entry+0x3c/0x50
[  218.531830]  rest_init+0xe4/0xf0
[  218.535054]  start_kernel+0x740/0x748
[  218.538712]  __primary_switched+0x80/0x90
[  279.451877] rcu: INFO: rcu_preempt detected stalls on CPUs/tasks:
[  279.457968] rcu:     0-....: (0 ticks this GP) idle=4ffc/1/0x4000000000000004 softirq=152/140441 fqs=32229
[  279.467353] rcu:     (detected by 3, t=68465 jiffies, g=-803, q=491 ncpus=6)
[  279.474130] Sending NMI from CPU 3 to CPUs 0:
[  279.474137] NMI backtrace for cpu 0
[  279.481966] CPU: 0 UID: 0 PID: 0 Comm: swapper/0 Not tainted 6.12.3-lts-next-g37d02f4dcbbe-dirty #1
[  279.491003] Hardware name: NXP i.MX95 19X19 board (DT)
[  279.496135] pstate: 60400009 (nZCv daif +PAN -UAO -TCO -DIT -SSBS BTYPE=--)
[  279.503078] pc : rcu_core+0x380/0xa00
[  279.506744] lr : rcu_core+0x164/0xa00
[  279.510401] sp : ffff800080003e80
[  279.513703] x29: ffff800080003ed0 x28: ffff8000823a8000 x27: ffff0003fdf410b8
[  279.520836] x26: ffff8000824a50c0 x25: ffff80008253a900 x24: ffff8000823bd040
[  279.527969] x23: ffff80037bb84000 x22: ffff8000824a8c98 x21: ffff0003fdf41040
[  279.535101] x20: ffff80008253a900 x19: ffff8000823bd051 x18: 00000000e4bb62d2
[  279.542234] x17: ffff80037bb84000 x16: ffff800080000000 x15: 00003d0900007d00
[  279.549367] x14: 0000000000017700 x13: 0000000000000000 x12: 003d090000000000
[  279.556499] x11: 0000000000000040 x10: ffff000080304170 x9 : ffff800080102034
[  279.563623] x8 : ffff0000820b54b0 x7 : 0000000000000000 x6 : 00000000f12b8298
[  279.570747] x5 : 00ffffffffffffff x4 : 0000000000000015 x3 : 0000000000c1d34d
[  279.577871] x2 : 0000000000000001 x1 : ffff0003fdf410b8 x0 : 0000000000000000
[  279.584995] Call trace:
[  279.587439]  rcu_core+0x380/0xa00
[  279.590750]  rcu_core_si+0x18/0x38
[  279.594147]  handle_softirqs+0x128/0x360
[  279.598056]  __do_softirq+0x1c/0x28
[  279.601531]  ____do_softirq+0x18/0x30
[  279.605180]  call_on_irq_stack+0x24/0x58
[  279.609089]  do_softirq_own_stack+0x24/0x50
[  279.613257]  irq_exit_rcu+0x90/0xd0
[  279.616741]  el1_interrupt+0x38/0x68
[  279.620312]  el1h_64_irq_handler+0x18/0x28
[  279.624403]  el1h_64_irq+0x64/0x68
[  279.627791]  cpuidle_enter_state+0x2c0/0x4e0
[  279.632047]  cpuidle_enter+0x40/0x60
[  279.635617]  do_idle+0x1ec/0x268
[  279.638841]  cpu_startup_entry+0x3c/0x50
[  279.642759]  rest_init+0xe4/0xf0
[  279.645983]  start_kernel+0x740/0x748
[  279.649640]  __primary_switched+0x80/0x90
[  281.839981] rcu: INFO: rcu_preempt detected expedited stalls on CPUs/tasks: { 0-.... } 69082 jiffies s: 57 root: 0x1/.
[  281.850675] rcu: blocking rcu_node structures (internal RCU debug):
[  281.856933] Sending NMI from CPU 4 to CPUs 0:
[  281.861286] NMI backtrace for cpu 0
[  281.864771] CPU: 0 UID: 0 PID: 0 Comm: swapper/0 Not tainted 6.12.3-lts-next-g37d02f4dcbbe-dirty #1
[  281.873802] Hardware name: NXP i.MX95 19X19 board (DT)
[  281.878924] pstate: 40400009 (nZcv daif +PAN -UAO -TCO -DIT -SSBS BTYPE=--)
[  281.885874] pc : handle_softirqs+0xb4/0x360
[  281.890051] lr : handle_softirqs+0x80/0x360
[  281.894220] sp : ffff800080003f40
[  281.897530] x29: ffff800080003f40 x28: ffff8000823a8000 x27: 00000000fd50dc10
[  281.904655] x26: ffff8000824a50c0 x25: 0000000000000000 x24: 00000000de70e408
[  281.911779] x23: 0000000080400009 x22: 0000000000000200 x21: ffff8000824a3d20
[  281.918903] x20: ffff8000800100e8 x19: 0000000000000000 x18: 00000000e4bb62d2
[  281.926027] x17: ffff80037bb84000 x16: ffff800080000000 x15: 0000000000000000
[  281.933151] x14: ffff8000824b5540 x13: 0000000000000358 x12: 0000000000000001
[  281.940275] x11: 0000000000000040 x10: ffff000080304170 x9 : ffff80008005cae8
[  281.947399] x8 : ffff0000820b54b0 x7 : 0000000000000000 x6 : 00000000f12b8298
[  281.954523] x5 : 00ffffffffffffff x4 : 0000000000000015 x3 : 0000000000c1d34d
[  281.961647] x2 : 00000041a029a521 x1 : ffff80037bb84000 x0 : ffff8000823bb580
[  281.968771] Call trace:
[  281.971206]  handle_softirqs+0xb4/0x360
[  281.975037]  __do_softirq+0x1c/0x28
[  281.978512]  ____do_softirq+0x18/0x30
[  281.982169]  call_on_irq_stack+0x24/0x58
[  281.986078]  do_softirq_own_stack+0x24/0x50
[  281.990247]  irq_exit_rcu+0x90/0xd0
[  281.993731]  el1_interrupt+0x38/0x68
[  281.997301]  el1h_64_irq_handler+0x18/0x28
[  282.001383]  el1h_64_irq+0x64/0x68
[  282.004781]  cpuidle_enter_state+0x2c0/0x4e0
[  282.009036]  cpuidle_enter+0x40/0x60
[  282.012607]  do_idle+0x1ec/0x268
[  282.015831]  cpu_startup_entry+0x3c/0x50
[  282.019748]  rest_init+0xe4/0xf0
[  282.022972]  start_kernel+0x740/0x748
[  282.026629]  __primary_switched+0x80/0x90

