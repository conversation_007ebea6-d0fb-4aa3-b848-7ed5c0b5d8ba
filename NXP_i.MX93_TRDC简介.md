# NXP i.MX93芯片TRDC简介

## 目录

1. [概述](#概述)
2. [TRDC架构](#trdc架构)
3. [核心功能](#核心功能)
4. [域管理](#域管理)
5. [访问控制机制](#访问控制机制)
6. [配置方法](#配置方法)
7. [应用场景](#应用场景)
8. [编程接口](#编程接口)
9. [安全特性](#安全特性)
10. [最佳实践](#最佳实践)

## 概述

### 什么是TRDC

TRDC (Trusted Resource Domain Controller) 是NXP i.MX93处理器中的核心安全组件，负责管理系统资源的访问控制和域隔离。它为多核异构系统提供了硬件级别的安全边界，确保不同安全域之间的资源访问得到严格控制。

### 设计目标

- **资源隔离**：在硬件层面实现不同安全域的资源隔离
- **访问控制**：精细化控制各个主控器对系统资源的访问权限
- **安全启动**：支持安全启动流程中的资源保护
- **运行时保护**：在系统运行时维护安全边界

## TRDC架构

### 系统架构图

```mermaid
graph TD
    A[ARM Cortex-A55] --> B[TRDC]
    C[ARM Cortex-M33] --> B
    D[NPU] --> B
    E[GPU] --> B
    F[DMA] --> B

    B --> G[DDR Memory]
    B --> H[OCRAM]
    B --> I[Peripherals]
    B --> J[System Registers]

    style B fill:#e1f5fe,stroke:#333,stroke-width:3px
    style G fill:#f3e5f5
    style H fill:#f3e5f5
    style I fill:#f3e5f5
    style J fill:#f3e5f5
```

### 核心组件

| 组件 | 功能 | 描述 |
|------|------|------|
| **Domain Assignment** | 域分配 | 将主控器分配到不同的安全域 |
| **Memory Region Controller (MRC)** | 内存区域控制 | 控制内存区域的访问权限 |
| **Peripheral Access Controller (PAC)** | 外设访问控制 | 管理外设的访问权限 |
| **Master Domain Assignment (MDA)** | 主控器域分配 | 配置主控器的域属性 |

## 核心功能

### 1. 域管理 (Domain Management)

TRDC支持多个安全域，每个域具有不同的安全级别：

```mermaid
graph LR
    A[Domain 0<br/>Secure] --> B[Domain 1<br/>Non-Secure]
    B --> C[Domain 2<br/>User]
    C --> D[Domain 3<br/>Guest]

    style A fill:#ffcdd2
    style B fill:#fff3e0
    style C fill:#e8f5e8
    style D fill:#f3e5f5
```

#### 域特性

- **Domain 0 (Secure)**：最高安全级别，通常分配给安全固件
- **Domain 1 (Non-Secure)**：非安全域，分配给操作系统内核
- **Domain 2 (User)**：用户域，分配给用户应用程序
- **Domain 3 (Guest)**：客户域，用于虚拟化环境

### 2. 内存保护 (Memory Protection)

#### 内存区域控制器 (MRC)

```c
// MRC配置示例
struct trdc_mrc_config {
    uint32_t start_addr;    // 起始地址
    uint32_t end_addr;      // 结束地址
    uint32_t domain_mask;   // 域访问掩码
    uint32_t access_policy; // 访问策略
};

// 典型的内存区域配置
static struct trdc_mrc_config ddr_regions[] = {
    {
        .start_addr = 0x80000000,
        .end_addr   = 0x8FFFFFFF,
        .domain_mask = DOMAIN_0_MASK,
        .access_policy = READ_WRITE_EXEC
    },
    {
        .start_addr = 0x90000000,
        .end_addr   = 0x9FFFFFFF,
        .domain_mask = DOMAIN_1_MASK | DOMAIN_2_MASK,
        .access_policy = READ_WRITE
    }
};
```

### 3. 外设访问控制 (Peripheral Access Control)

#### PAC配置

```c
// 外设访问控制配置
struct trdc_pac_config {
    uint32_t peripheral_id;  // 外设ID
    uint32_t domain_access;  // 域访问权限
    uint32_t secure_access;  // 安全访问控制
};

// UART外设访问配置示例
static struct trdc_pac_config uart_config = {
    .peripheral_id = UART1_BASE,
    .domain_access = DOMAIN_0_ACCESS | DOMAIN_1_ACCESS,
    .secure_access = SECURE_PRIVILEGED
};
```

## 域管理

### 主控器域分配 (MDA)

不同的主控器可以被分配到不同的安全域：

| 主控器 | 默认域 | 可配置域 | 用途 |
|--------|--------|----------|------|
| **Cortex-A55** | Domain 1 | 0-3 | 应用处理器 |
| **Cortex-M33** | Domain 0 | 0-1 | 安全处理器 |
| **NPU** | Domain 2 | 1-3 | AI加速器 |
| **GPU** | Domain 2 | 1-3 | 图形处理器 |
| **DMA** | Domain 1 | 0-3 | 直接内存访问 |

### 域切换机制

```c
// 域切换API示例
int trdc_switch_domain(uint32_t master_id, uint32_t target_domain)
{
    // 验证域切换权限
    if (!trdc_check_domain_switch_permission(master_id, target_domain)) {
        return -EPERM;
    }

    // 执行域切换
    trdc_set_master_domain(master_id, target_domain);

    // 更新访问权限
    trdc_update_access_permissions(master_id);

    return 0;
}
```

## 访问控制机制

### 访问权限矩阵

```mermaid
graph TD
    A[访问请求] --> B{检查域ID}
    B --> C{检查资源权限}
    C --> D{检查安全属性}
    D --> E[允许访问]
    D --> F[拒绝访问]

    style E fill:#c8e6c9
    style F fill:#ffcdd2
```

### 权限级别

| 权限 | 描述 | 适用场景 |
|------|------|----------|
| **No Access** | 禁止访问 | 严格隔离的资源 |
| **Read Only** | 只读访问 | 配置寄存器、状态信息 |
| **Write Only** | 只写访问 | 控制寄存器 |
| **Read/Write** | 读写访问 | 数据缓冲区 |
| **Execute** | 执行权限 | 代码段 |

### 安全属性

```c
// 安全属性定义
#define TRDC_SECURE_ACCESS      (1 << 0)
#define TRDC_PRIVILEGED_ACCESS  (1 << 1)
#define TRDC_USER_ACCESS        (1 << 2)
#define TRDC_DEBUG_ACCESS       (1 << 3)

// 访问控制策略
struct trdc_access_policy {
    uint32_t read_domains;      // 可读域掩码
    uint32_t write_domains;     // 可写域掩码
    uint32_t execute_domains;   // 可执行域掩码
    uint32_t secure_attr;       // 安全属性
};
```

## 配置方法

### 1. 设备树配置

```dts
// i.MX93设备树中的TRDC配置
trdc: trdc@44270000 {
    compatible = "fsl,imx93-trdc";
    reg = <0x44270000 0x10000>;
    interrupts = <GIC_SPI 131 IRQ_TYPE_LEVEL_HIGH>;

    // 内存区域配置
    memory-regions {
        secure-region {
            start-address = <0x80000000>;
            size = <0x10000000>;
            domain-access = <0x01>; // Domain 0 only
        };

        non-secure-region {
            start-address = <0x90000000>;
            size = <0x10000000>;
            domain-access = <0x0E>; // Domain 1-3
        };
    };

    // 外设访问配置
    peripheral-access {
        uart1 {
            domain-access = <0x03>; // Domain 0-1
            secure-access = "privileged";
        };
    };
};
```

### 2. 运行时配置

```c
// TRDC初始化函数
int trdc_init(void)
{
    // 1. 启用TRDC时钟
    trdc_enable_clock();

    // 2. 配置内存区域
    trdc_configure_memory_regions();

    // 3. 配置外设访问
    trdc_configure_peripheral_access();

    // 4. 配置主控器域分配
    trdc_configure_master_domains();

    // 5. 启用TRDC
    trdc_enable();

    return 0;
}

// 内存区域配置
static void trdc_configure_memory_regions(void)
{
    // DDR安全区域配置
    trdc_mrc_set_region(MRC_DDR, 0,
                       0x80000000, 0x8FFFFFFF,
                       DOMAIN_0_MASK, READ_WRITE_EXEC);

    // DDR非安全区域配置
    trdc_mrc_set_region(MRC_DDR, 1,
                       0x90000000, 0x9FFFFFFF,
                       DOMAIN_1_MASK | DOMAIN_2_MASK,
                       READ_WRITE_EXEC);

    // OCRAM配置
    trdc_mrc_set_region(MRC_OCRAM, 0,
                       0x20480000, 0x204BFFFF,
                       DOMAIN_0_MASK,
                       READ_WRITE_EXEC);
}
```

## 应用场景

### 1. 安全启动

TRDC在安全启动过程中发挥关键作用：

```mermaid
sequenceDiagram
    participant ROM as Boot ROM
    participant ATF as ARM Trusted Firmware
    participant UBOOT as U-Boot
    participant LINUX as Linux Kernel
    participant TRDC as TRDC

    ROM->>TRDC: 初始化基本域配置
    ROM->>ATF: 加载ATF到安全域
    ATF->>TRDC: 配置安全域资源
    ATF->>UBOOT: 启动U-Boot
    UBOOT->>TRDC: 配置非安全域
    UBOOT->>LINUX: 启动Linux
    LINUX->>TRDC: 运行时域管理
```

### 2. 虚拟化支持

```c
// 虚拟化环境中的TRDC配置
struct vm_trdc_config {
    uint32_t vm_id;
    uint32_t domain_id;
    struct trdc_memory_region *memory_regions;
    struct trdc_peripheral_access *peripheral_access;
};

// 为虚拟机配置TRDC
int trdc_configure_vm(struct vm_trdc_config *vm_config)
{
    // 分配专用域
    trdc_allocate_domain(vm_config->vm_id, vm_config->domain_id);

    // 配置内存隔离
    trdc_setup_vm_memory(vm_config->memory_regions);

    // 配置外设访问
    trdc_setup_vm_peripherals(vm_config->peripheral_access);

    return 0;
}
```

### 3. 多核异构系统

在i.MX93的多核环境中，TRDC确保不同核心之间的安全隔离：

| 核心 | 域分配 | 访问权限 | 典型用途 |
|------|--------|----------|----------|
| **Cortex-A55 Core 0** | Domain 1 | 系统资源 | Linux内核 |
| **Cortex-A55 Core 1** | Domain 2 | 用户资源 | 用户应用 |
| **Cortex-M33** | Domain 0 | 安全资源 | 安全服务 |

## 编程接口

### 1. 寄存器接口

```c
// TRDC寄存器定义
#define TRDC_BASE_ADDR          0x44270000

// 主要寄存器偏移
#define TRDC_CR                 0x0000  // 控制寄存器
#define TRDC_HWCFG0             0x0010  // 硬件配置0
#define TRDC_HWCFG1             0x0014  // 硬件配置1
#define TRDC_MDA_W0_DFMT0       0x0800  // 主控器域分配
#define TRDC_MRC_GLBCFG         0x1000  // MRC全局配置
#define TRDC_PAC_GLBCFG         0x2000  // PAC全局配置

// 寄存器操作宏
#define TRDC_READ(reg)          readl(TRDC_BASE_ADDR + (reg))
#define TRDC_WRITE(reg, val)    writel((val), TRDC_BASE_ADDR + (reg))
```

### 2. 高级API

```c
// TRDC管理API
struct trdc_manager {
    void __iomem *base;
    struct device *dev;
    spinlock_t lock;
};

// 初始化TRDC管理器
int trdc_manager_init(struct trdc_manager *mgr, void __iomem *base);

// 域操作API
int trdc_create_domain(struct trdc_manager *mgr, uint32_t domain_id);
int trdc_destroy_domain(struct trdc_manager *mgr, uint32_t domain_id);
int trdc_assign_master_to_domain(struct trdc_manager *mgr,
                                 uint32_t master_id, uint32_t domain_id);

// 内存区域操作API
int trdc_add_memory_region(struct trdc_manager *mgr,
                          struct trdc_memory_region *region);
int trdc_remove_memory_region(struct trdc_manager *mgr, uint32_t region_id);
int trdc_update_memory_permissions(struct trdc_manager *mgr,
                                  uint32_t region_id, uint32_t permissions);

// 外设访问控制API
int trdc_grant_peripheral_access(struct trdc_manager *mgr,
                                uint32_t peripheral_id, uint32_t domain_mask);
int trdc_revoke_peripheral_access(struct trdc_manager *mgr,
                                 uint32_t peripheral_id, uint32_t domain_mask);
```

### 3. Linux内核驱动

```c
// TRDC Linux驱动框架
static const struct of_device_id trdc_of_match[] = {
    { .compatible = "fsl,imx93-trdc", },
    { /* sentinel */ }
};

static struct platform_driver trdc_driver = {
    .probe = trdc_probe,
    .remove = trdc_remove,
    .driver = {
        .name = "imx93-trdc",
        .of_match_table = trdc_of_match,
    },
};

// 驱动探测函数
static int trdc_probe(struct platform_device *pdev)
{
    struct trdc_manager *mgr;
    struct resource *res;
    int ret;

    mgr = devm_kzalloc(&pdev->dev, sizeof(*mgr), GFP_KERNEL);
    if (!mgr)
        return -ENOMEM;

    res = platform_get_resource(pdev, IORESOURCE_MEM, 0);
    mgr->base = devm_ioremap_resource(&pdev->dev, res);
    if (IS_ERR(mgr->base))
        return PTR_ERR(mgr->base);

    mgr->dev = &pdev->dev;
    spin_lock_init(&mgr->lock);

    ret = trdc_manager_init(mgr, mgr->base);
    if (ret)
        return ret;

    platform_set_drvdata(pdev, mgr);

    return 0;
}
```

## 安全特性

### 1. 防篡改保护

```c
// TRDC配置锁定机制
void trdc_lock_configuration(void)
{
    uint32_t cr_val;

    // 读取当前控制寄存器值
    cr_val = TRDC_READ(TRDC_CR);

    // 设置配置锁定位
    cr_val |= TRDC_CR_GLOCK;

    // 写回控制寄存器
    TRDC_WRITE(TRDC_CR, cr_val);

    // 验证锁定状态
    if (!(TRDC_READ(TRDC_CR) & TRDC_CR_GLOCK)) {
        panic("TRDC configuration lock failed!");
    }
}
```

### 2. 违规检测

```c
// TRDC违规处理
static irqreturn_t trdc_violation_handler(int irq, void *data)
{
    struct trdc_manager *mgr = data;
    uint32_t violation_status;
    uint32_t violation_addr;
    uint32_t violation_info;

    // 读取违规状态
    violation_status = TRDC_READ(TRDC_VIOLATION_STATUS);
    violation_addr = TRDC_READ(TRDC_VIOLATION_ADDR);
    violation_info = TRDC_READ(TRDC_VIOLATION_INFO);

    // 记录违规信息
    dev_err(mgr->dev, "TRDC Violation Detected:\n");
    dev_err(mgr->dev, "  Address: 0x%08x\n", violation_addr);
    dev_err(mgr->dev, "  Domain: %d\n",
            (violation_info >> 16) & 0xF);
    dev_err(mgr->dev, "  Master: %d\n",
            (violation_info >> 8) & 0xFF);
    dev_err(mgr->dev, "  Access Type: %s\n",
            (violation_info & 0x1) ? "Write" : "Read");

    // 清除违规状态
    TRDC_WRITE(TRDC_VIOLATION_STATUS, violation_status);

    return IRQ_HANDLED;
}
```

### 3. 调试支持

```c
// TRDC调试接口
#ifdef CONFIG_DEBUG_FS

static int trdc_debug_show(struct seq_file *s, void *data)
{
    struct trdc_manager *mgr = s->private;
    int i;

    seq_printf(s, "TRDC Configuration:\n");
    seq_printf(s, "==================\n");

    // 显示域配置
    seq_printf(s, "Domain Configuration:\n");
    for (i = 0; i < TRDC_MAX_DOMAINS; i++) {
        uint32_t domain_config = trdc_get_domain_config(mgr, i);
        seq_printf(s, "  Domain %d: 0x%08x\n", i, domain_config);
    }

    // 显示内存区域配置
    seq_printf(s, "\nMemory Regions:\n");
    for (i = 0; i < TRDC_MAX_MRC_REGIONS; i++) {
        struct trdc_memory_region region;
        if (trdc_get_memory_region(mgr, i, &region) == 0) {
            seq_printf(s, "  Region %d: 0x%08x-0x%08x, Domain: 0x%x\n",
                      i, region.start_addr, region.end_addr,
                      region.domain_mask);
        }
    }

    return 0;
}

static int trdc_debug_open(struct inode *inode, struct file *file)
{
    return single_open(file, trdc_debug_show, inode->i_private);
}

static const struct file_operations trdc_debug_fops = {
    .open = trdc_debug_open,
    .read = seq_read,
    .llseek = seq_lseek,
    .release = single_release,
};

static void trdc_create_debugfs(struct trdc_manager *mgr)
{
    mgr->debug_dir = debugfs_create_dir("trdc", NULL);
    debugfs_create_file("config", 0444, mgr->debug_dir, mgr,
                       &trdc_debug_fops);
}

#endif /* CONFIG_DEBUG_FS */
```

## 最佳实践

### 1. 安全配置原则

```c
// 安全配置最佳实践
static const struct trdc_security_policy secure_policy = {
    .principle = "最小权限原则",
    .guidelines = {
        "只授予必要的最小访问权限",
        "默认拒绝所有访问",
        "定期审查和更新权限配置",
        "启用违规检测和日志记录"
    }
};

// 推荐的域分配策略
static void trdc_apply_security_policy(struct trdc_manager *mgr)
{
    // 1. 配置安全域（Domain 0）
    trdc_configure_secure_domain(mgr);

    // 2. 配置非安全域（Domain 1-3）
    trdc_configure_nonsecure_domains(mgr);

    // 3. 启用违规检测
    trdc_enable_violation_detection(mgr);

    // 4. 锁定关键配置
    trdc_lock_critical_configuration(mgr);
}
```

### 2. 性能优化

```c
// TRDC性能优化建议
static void trdc_optimize_performance(struct trdc_manager *mgr)
{
    // 1. 合理规划内存区域，减少区域数量
    trdc_optimize_memory_regions(mgr);

    // 2. 使用缓存友好的访问模式
    trdc_enable_access_caching(mgr);

    // 3. 避免频繁的域切换
    trdc_minimize_domain_switches(mgr);

    // 4. 优化中断处理
    trdc_optimize_interrupt_handling(mgr);
}
```

### 3. 故障排除

```c
// TRDC故障排除工具
struct trdc_diagnostic {
    uint32_t violation_count;
    uint32_t last_violation_addr;
    uint32_t last_violation_domain;
    uint32_t configuration_errors;
};

static void trdc_run_diagnostics(struct trdc_manager *mgr,
                                struct trdc_diagnostic *diag)
{
    // 检查配置一致性
    trdc_check_configuration_consistency(mgr, diag);

    // 检查违规历史
    trdc_check_violation_history(mgr, diag);

    // 验证域配置
    trdc_validate_domain_configuration(mgr, diag);

    // 测试访问权限
    trdc_test_access_permissions(mgr, diag);
}
```

## 总结

NXP i.MX93的TRDC是一个功能强大的硬件安全组件，为多核异构系统提供了完整的资源访问控制和域隔离解决方案。通过合理配置和使用TRDC，可以实现：

### 关键优势

1. **硬件级安全**：在硬件层面实现安全边界，不依赖软件实现
2. **细粒度控制**：支持内存和外设的精细化访问控制
3. **多域支持**：支持多个安全域的并行运行
4. **性能优化**：硬件实现确保最小的性能开销
5. **灵活配置**：支持运行时动态配置和管理

### 应用价值

- **嵌入式安全**：为IoT和边缘计算设备提供硬件安全保障
- **虚拟化支持**：为虚拟化环境提供硬件隔离能力
- **多租户系统**：支持多个独立应用的安全运行
- **安全启动**：确保系统启动过程的安全性

TRDC作为i.MX93安全架构的核心组件，为现代嵌入式系统的安全需求提供了完整的硬件解决方案。