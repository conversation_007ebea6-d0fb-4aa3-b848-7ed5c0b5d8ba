#!/bin/bash
# apply-ecspi-fix.sh
# Script to apply ECSPI DMA fix for i.MX6ULL kernel 4.19.35

set -e

KERNEL_DIR="/path/to/linux-4.19.35"
PATCH_FILE="0001-spi-imx-fix-dma-burst-length-for-continuous-transfer.patch"
BACKUP_DIR="backup_$(date +%Y%m%d_%H%M%S)"

echo "=== i.MX6ULL ECSPI DMA修复脚本 ==="
echo "内核目录: $KERNEL_DIR"
echo "补丁文件: $PATCH_FILE"

# 检查必要文件
if [ ! -f "$PATCH_FILE" ]; then
    echo "错误: 补丁文件 $PATCH_FILE 不存在"
    exit 1
fi

if [ ! -d "$KERNEL_DIR" ]; then
    echo "错误: 内核目录 $KERNEL_DIR 不存在"
    echo "请修改脚本中的KERNEL_DIR变量指向正确的内核源码目录"
    exit 1
fi

if [ ! -f "$KERNEL_DIR/drivers/spi/spi-imx.c" ]; then
    echo "错误: 找不到 spi-imx.c 驱动文件"
    exit 1
fi

echo "检查通过，开始应用补丁..."

# 进入内核目录
cd "$KERNEL_DIR"

# 创建备份
echo "创建备份目录: $BACKUP_DIR"
mkdir -p "$BACKUP_DIR"
cp drivers/spi/spi-imx.c "$BACKUP_DIR/"

# 检查补丁是否可以应用
echo "检查补丁兼容性..."
if ! git apply --check "../$PATCH_FILE" 2>/dev/null; then
    echo "警告: 补丁可能不完全兼容，尝试强制应用..."
    if ! patch -p1 --dry-run < "../$PATCH_FILE"; then
        echo "错误: 补丁无法应用，请手动修改代码"
        exit 1
    fi
fi

# 应用补丁
echo "应用补丁..."
if git apply "../$PATCH_FILE" 2>/dev/null; then
    echo "✓ 使用git apply成功应用补丁"
elif patch -p1 < "../$PATCH_FILE"; then
    echo "✓ 使用patch命令成功应用补丁"
else
    echo "错误: 补丁应用失败"
    exit 1
fi

echo "补丁应用成功！"

# 验证修改
echo "验证修改..."
if grep -q "spi_imx_configure_burst_length" drivers/spi/spi-imx.c; then
    echo "✓ 发现新增的burst length配置函数"
else
    echo "警告: 未找到预期的修改"
fi

if grep -q "spi_imx_configure_dma_thresholds" drivers/spi/spi-imx.c; then
    echo "✓ 发现新增的DMA阈值配置函数"
else
    echo "警告: 未找到预期的DMA阈值修改"
fi

echo ""
echo "=== 下一步操作 ==="
echo "1. 重新编译内核:"
echo "   make ARCH=arm CROSS_COMPILE=arm-linux-gnueabihf- zImage"
echo "   make ARCH=arm CROSS_COMPILE=arm-linux-gnueabihf- modules"
echo "   make ARCH=arm CROSS_COMPILE=arm-linux-gnueabihf- dtbs"
echo ""
echo "2. 安装新内核到目标设备"
echo ""
echo "3. 重启设备并测试ECSPI DMA传输"
echo ""
echo "4. 使用以下命令验证寄存器配置:"
echo "   ./memtool ECSPI2.CONREG"
echo "   ./memtool ECSPI2.DMAREG"
echo ""
echo "备份文件保存在: $BACKUP_DIR/"

# 生成测试脚本
cat > test_ecspi_fix.c << 'EOF'
/*
 * test_ecspi_fix.c - Test program for ECSPI DMA fix
 * Compile: arm-linux-gnueabihf-gcc -o test_ecspi_fix test_ecspi_fix.c
 */
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/ioctl.h>
#include <linux/spi/spidev.h>
#include <sys/time.h>
#include <string.h>

#define SPI_DEVICE "/dev/spidev1.0"
#define TEST_SIZE 1024

int main(void)
{
    int fd;
    uint8_t tx_buf[TEST_SIZE];
    uint8_t rx_buf[TEST_SIZE];
    struct spi_ioc_transfer tr;
    struct timeval start, end;
    int ret;
    
    printf("=== ECSPI DMA修复测试程序 ===\n");
    
    // 打开SPI设备
    fd = open(SPI_DEVICE, O_RDWR);
    if (fd < 0) {
        perror("无法打开SPI设备");
        return 1;
    }
    
    // 设置SPI模式
    uint8_t mode = SPI_MODE_0;
    ret = ioctl(fd, SPI_IOC_WR_MODE, &mode);
    if (ret < 0) {
        perror("设置SPI模式失败");
        close(fd);
        return 1;
    }
    
    // 设置SPI速度
    uint32_t speed = 20000000; // 20MHz
    ret = ioctl(fd, SPI_IOC_WR_MAX_SPEED_HZ, &speed);
    if (ret < 0) {
        perror("设置SPI速度失败");
        close(fd);
        return 1;
    }
    
    // 准备测试数据
    for (int i = 0; i < TEST_SIZE; i++) {
        tx_buf[i] = i & 0xFF;
    }
    memset(rx_buf, 0, TEST_SIZE);
    
    // 配置传输
    memset(&tr, 0, sizeof(tr));
    tr.tx_buf = (unsigned long)tx_buf;
    tr.rx_buf = (unsigned long)rx_buf;
    tr.len = TEST_SIZE;
    tr.speed_hz = speed;
    tr.bits_per_word = 8;
    
    printf("开始%d字节DMA传输测试...\n", TEST_SIZE);
    
    // 执行传输并计时
    gettimeofday(&start, NULL);
    ret = ioctl(fd, SPI_IOC_MESSAGE(1), &tr);
    gettimeofday(&end, NULL);
    
    if (ret < 0) {
        perror("SPI传输失败");
        close(fd);
        return 1;
    }
    
    // 计算传输时间和速率
    long duration = (end.tv_sec - start.tv_sec) * 1000000 + 
                   (end.tv_usec - start.tv_usec);
    
    printf("传输完成!\n");
    printf("传输时间: %ld 微秒\n", duration);
    printf("有效速率: %.2f MB/s\n", 
           (TEST_SIZE * 1000000.0) / duration / 1024 / 1024);
    
    // 验证数据（如果是回环测试）
    int errors = 0;
    for (int i = 0; i < TEST_SIZE; i++) {
        if (tx_buf[i] != rx_buf[i]) {
            errors++;
        }
    }
    
    if (errors == 0) {
        printf("✓ 数据验证通过（回环测试）\n");
    } else {
        printf("数据验证: %d个错误（可能不是回环测试）\n", errors);
    }
    
    close(fd);
    
    printf("\n请使用示波器检查SPI波形，确认字节间无间隔\n");
    
    return 0;
}
EOF

echo "已生成测试程序: test_ecspi_fix.c"
echo "编译命令: arm-linux-gnueabihf-gcc -o test_ecspi_fix test_ecspi_fix.c"

echo ""
echo "修复完成！请按照上述步骤重新编译和测试内核。"
