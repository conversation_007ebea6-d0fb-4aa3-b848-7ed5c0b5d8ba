#!/bin/bash

# i.MX91 以太网快速唤醒优化脚本
# 解决从待机模式唤醒后网口ping通需要5秒的问题
# 
# 使用方法：
# sudo ./imx91_ethernet_wakeup_optimization.sh [install|test|status|remove]

SCRIPT_NAME="i.MX91 Ethernet Wakeup Optimization"
ETH_INTERFACE="eth0"
UDEV_RULE_FILE="/etc/udev/rules.d/99-ethernet-wakeup.rules"
SYSTEMD_SERVICE_FILE="/etc/systemd/system/ethernet-resume.service"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 检查网口是否存在
check_ethernet_interface() {
    if ! ip link show $ETH_INTERFACE &>/dev/null; then
        log_error "网口 $ETH_INTERFACE 不存在"
        log_info "可用的网口："
        ip link show | grep -E "^[0-9]+:" | cut -d: -f2 | tr -d ' '
        exit 1
    fi
}

# 检查ethtool是否安装
check_ethtool() {
    if ! command -v ethtool &> /dev/null; then
        log_warning "ethtool 未安装，正在安装..."
        if command -v apt-get &> /dev/null; then
            apt-get update && apt-get install -y ethtool
        elif command -v yum &> /dev/null; then
            yum install -y ethtool
        else
            log_error "无法自动安装ethtool，请手动安装"
            exit 1
        fi
    fi
}

# 安装优化配置
install_optimization() {
    log_info "开始安装 $SCRIPT_NAME..."
    
    # 1. 配置网口电源管理
    log_info "配置网口电源管理..."
    echo enabled > /sys/class/net/$ETH_INTERFACE/device/power/wakeup
    echo on > /sys/class/net/$ETH_INTERFACE/device/power/control
    
    # 2. 启用Wake-on-LAN
    log_info "启用Wake-on-LAN功能..."
    ethtool -s $ETH_INTERFACE wol g
    
    # 3. 创建udev规则
    log_info "创建udev规则..."
    cat > $UDEV_RULE_FILE << EOF
# i.MX91 Ethernet Wakeup Rule
SUBSYSTEM=="net", ACTION=="add", DRIVERS=="fec", ATTR{power/wakeup}="enabled"
SUBSYSTEM=="net", ACTION=="add", DRIVERS=="fec", ATTR{power/control}="on"
EOF
    
    # 4. 创建systemd服务
    log_info "创建快速恢复服务..."
    cat > $SYSTEMD_SERVICE_FILE << EOF
[Unit]
Description=i.MX91 Fast Ethernet Resume
After=suspend.target hibernate.target hybrid-sleep.target

[Service]
Type=oneshot
ExecStart=/bin/bash -c 'ethtool -r $ETH_INTERFACE; sleep 0.1; ip link set $ETH_INTERFACE up; ethtool -s $ETH_INTERFACE wol g'
RemainAfterExit=yes

[Install]
WantedBy=suspend.target hibernate.target hybrid-sleep.target
EOF
    
    # 5. 启用服务
    systemctl daemon-reload
    systemctl enable ethernet-resume.service
    
    # 6. 重新加载udev规则
    udevadm control --reload-rules
    udevadm trigger
    
    log_success "优化配置安装完成！"
    log_info "建议重启系统以确保所有配置生效"
}

# 测试配置
test_configuration() {
    log_info "测试当前配置..."
    
    # 检查Wake-on-LAN状态
    log_info "检查Wake-on-LAN状态："
    ethtool $ETH_INTERFACE | grep "Wake-on"
    
    # 检查电源管理状态
    log_info "检查电源管理状态："
    echo "Wakeup: $(cat /sys/class/net/$ETH_INTERFACE/device/power/wakeup 2>/dev/null || echo 'N/A')"
    echo "Control: $(cat /sys/class/net/$ETH_INTERFACE/device/power/control 2>/dev/null || echo 'N/A')"
    
    # 检查服务状态
    log_info "检查systemd服务状态："
    systemctl is-enabled ethernet-resume.service 2>/dev/null || echo "服务未启用"
    
    # 显示MAC地址（用于Magic Packet测试）
    MAC_ADDR=$(ip link show $ETH_INTERFACE | grep -o -E '([[:xdigit:]]{1,2}:){5}[[:xdigit:]]{1,2}')
    log_info "网口MAC地址: $MAC_ADDR"
    log_info "Magic Packet测试命令: wakeonlan $MAC_ADDR"
}

# 显示状态
show_status() {
    log_info "$SCRIPT_NAME 状态检查"
    echo "=================================="
    
    # 检查文件是否存在
    if [[ -f $UDEV_RULE_FILE ]]; then
        log_success "udev规则已安装"
    else
        log_warning "udev规则未安装"
    fi
    
    if [[ -f $SYSTEMD_SERVICE_FILE ]]; then
        log_success "systemd服务已安装"
    else
        log_warning "systemd服务未安装"
    fi
    
    # 检查服务状态
    if systemctl is-enabled ethernet-resume.service &>/dev/null; then
        log_success "ethernet-resume服务已启用"
    else
        log_warning "ethernet-resume服务未启用"
    fi
    
    test_configuration
}

# 移除配置
remove_optimization() {
    log_info "移除 $SCRIPT_NAME..."
    
    # 停止并禁用服务
    systemctl stop ethernet-resume.service 2>/dev/null
    systemctl disable ethernet-resume.service 2>/dev/null
    
    # 删除文件
    rm -f $SYSTEMD_SERVICE_FILE
    rm -f $UDEV_RULE_FILE
    
    # 重新加载
    systemctl daemon-reload
    udevadm control --reload-rules
    
    log_success "优化配置已移除"
}

# 显示使用帮助
show_help() {
    echo "用法: $0 [install|test|status|remove|help]"
    echo ""
    echo "命令："
    echo "  install  - 安装网口快速唤醒优化配置"
    echo "  test     - 测试当前配置"
    echo "  status   - 显示配置状态"
    echo "  remove   - 移除优化配置"
    echo "  help     - 显示此帮助信息"
    echo ""
    echo "示例："
    echo "  sudo $0 install    # 安装优化配置"
    echo "  sudo $0 test       # 测试配置"
    echo "  sudo $0 status     # 查看状态"
}

# 主函数
main() {
    case "${1:-help}" in
        install)
            check_root
            check_ethernet_interface
            check_ethtool
            install_optimization
            ;;
        test)
            check_ethernet_interface
            check_ethtool
            test_configuration
            ;;
        status)
            show_status
            ;;
        remove)
            check_root
            remove_optimization
            ;;
        help|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
